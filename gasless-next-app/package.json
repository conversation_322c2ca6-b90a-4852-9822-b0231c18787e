{"name": "gasless-next-app", "private": true, "version": "0.0.19", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@rainbow-me/rainbowkit": "^1.2.0", "abitype": "^0.10.2", "ethers": "^6.8.1", "next": "^13.4.19", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "swr": "^2.2.4", "viem": "~1.16.3", "wagmi": "~1.4.3", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^18.16.12", "@types/qs": "^6.9.9", "@types/react": "^18.2.21", "autoprefixer": "^10.4.16", "eslint": "^8.15.0", "eslint-config-next": "^13.4.19", "next": "^13.4.19", "tailwindcss": "^3.3.5", "typescript": "^5.0.4"}, "engines": {"node": ">=16.8.0"}}
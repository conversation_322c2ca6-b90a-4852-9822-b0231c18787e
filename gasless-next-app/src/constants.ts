import type { Address } from "wagmi";

export const MAX_ALLOWANCE =
  115792089237316195423570985008687907853269984665640564039457584007913129639935n;

export const POLYGON_EXCHANGE_PROXY =
  "******************************************";
export const ETHEREUM_EXCHANGE_PROXY =
  "******************************************";
export const ARBITRUM_EXCHANGE_PROXY =
  "******************************************";

interface Token {
  name: string;
  address: Address;
  symbol: string;
  decimals: number;
  chainId: number;
  logoURI: string;
}

export const POLYGON_TOKENS: Token[] = [
  {
    chainId: 137,
    name: "Wrapped Matic",
    symbol: "WMATIC",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/matic.svg",
  },
  {
    chainId: 137,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
];

export const POLYGON_TOKENS_BY_SYMBOL: Record<string, Token> = {
  wmatic: {
    chainId: 137,
    name: "Wrapped Matic",
    symbol: "WMATIC",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/matic.svg",
  },
  usdc: {
    chainId: 137,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
};

export const POLYGON_TOKENS_BY_ADDRESS: Record<string, Token> = {
  "******************************************": {
    chainId: 137,
    name: "Wrapped Matic",
    symbol: "WMATIC",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/matic.svg",
  },
  "******************************************": {
    chainId: 137,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
};

export const ETHEREUM_TOKENS: Token[] = [
  {
    chainId: 1,
    name: "Balancer",
    symbol: "BAL",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/balancer/brand-assets/main/logo/circle-container/logo-balancer-black-128x128.svg",
  },
  {
    chainId: 1,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
];

export const ETHEREUM_TOKENS_BY_SYMBOL: Record<string, Token> = {
  bal: {
    chainId: 1,
    name: "Balancer",
    symbol: "BAL",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/balancer/brand-assets/main/logo/circle-container/logo-balancer-black-128x128.svg",
  },
  usdc: {
    chainId: 1,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
};

export const ETHEREUM_TOKENS_BY_ADDRESS: Record<string, Token> = {
  "******************************************": {
    chainId: 1,
    name: "Balancer",
    symbol: "BAL",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/balancer/brand-assets/main/logo/circle-container/logo-balancer-black-128x128.svg",
  },
  "******************************************": {
    chainId: 1,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
};

export const ARBITRUM_TOKENS: Token[] = [
  {
    chainId: 42161,
    name: "Wrapped Ether",
    symbol: "WETH",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/OffchainLabs/arbitrum-classic/master/docs/assets/arbitrum_logo.svg",
  },
  {
    chainId: 42161,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
];

export const ARBITRUM_TOKENS_BY_SYMBOL: Record<string, Token> = {
  weth: {
    chainId: 42161,
    name: "Wrapped Ether",
    symbol: "WETH",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/OffchainLabs/arbitrum-classic/master/docs/assets/arbitrum_logo.svg",
  },
  usdc: {
    chainId: 42161,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
};

export const ARBITRUM_TOKENS_BY_ADDRESS: Record<string, Token> = {
  "******************************************": {
    chainId: 42161,
    name: "Wrapped Ether",
    symbol: "WETH",
    decimals: 18,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/OffchainLabs/arbitrum-classic/master/docs/assets/arbitrum_logo.svg",
  },
  "******************************************": {
    chainId: 42161,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logoURI:
      "https://raw.githubusercontent.com/maticnetwork/polygon-token-assets/main/assets/tokenAssets/usdc.svg",
  },
};

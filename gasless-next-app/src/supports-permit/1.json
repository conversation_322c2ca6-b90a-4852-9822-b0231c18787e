{"0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48": {"kind": "permit", "domain": {"name": "USD Coin", "version": "2", "chainId": 1, "verifyingContract": "0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48"}, "domainSeparator": "0x06c37168a7db5138defc7866392bb87a741f9b3d104deb5094588ce041cae335", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba100000625a3754423978a60c9317c58a424e3d": {"kind": "permit", "domain": {"name": "Balancer", "version": "1", "chainId": 1, "verifyingContract": "0xba100000625a3754423978a60c9317c58a424e3d"}, "domainSeparator": "0x0f7e6db4bd29f5b0743e892c53690ee939ed780f756e0d021b93ed86993b03f4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x27702a26126e0b3702af63ee09ac4d1a084ef628": {"kind": "permit", "domain": {"name": "aleph.im v2", "version": "1", "chainId": 1, "verifyingContract": "0x27702a26126e0b3702af63ee09ac4d1a084ef628"}, "domainSeparator": "0xe8e7847d679a370f16f2085e3a93c64d0668790a5ac89589212c08aa9ed093f0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd46ba6d942050d489dbd938a2c909a5d5039a161": {"kind": "permit", "domain": {"name": "Ampleforth", "version": "1", "chainId": 1, "verifyingContract": "0xd46ba6d942050d489dbd938a2c909a5d5039a161"}, "domainSeparator": "0x8452f96051b7d134b6a9d79eb85945b6f224783cdea0a9209a5bcc17719af1e3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0aacfbec6a24756c20d41914f2caba817c0d8521": {"kind": "permit", "domain": {"name": "YAM", "chainId": 1, "verifyingContract": "0x0aacfbec6a24756c20d41914f2caba817c0d8521"}, "domainSeparator": "0x0831004d8ff41c035e313de925d8d0a82f503594261110fbd026bf652dba202b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x362bc847a3a9637d3af6624eec853618a43ed7d2": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x362bc847a3a9637d3af6624eec853618a43ed7d2"}, "domainSeparator": "0x2d97fe5dc3cc4e455758868061837504e14f90314f2f7046ec9b015a94ce254f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2edf094db69d6dcd487f1b3db9febe2eec0dd4c5": {"kind": "permit", "domain": {"name": "ZeroSwapToken", "version": "1", "chainId": 1, "verifyingContract": "0x2edf094db69d6dcd487f1b3db9febe2eec0dd4c5"}, "domainSeparator": "0x2cc64d06833cc55e3b554b6df8f6e78d58aafc9dd907ce74e96e60d5312742c4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x18aaa7115705e8be94bffebde57af9bfc265b998": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x18aaa7115705e8be94bffebde57af9bfc265b998"}, "domainSeparator": "0x65433ea59c7f020ffb492d8f80a4c088ecf0a0542db4a4551e3f98cf0c5819fd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0202be363b8a4820f3f4de7faf5224ff05943ab1": {"kind": "permit", "domain": {"name": "UniLend Finance Token", "version": "1", "chainId": 1, "verifyingContract": "0x0202be363b8a4820f3f4de7faf5224ff05943ab1"}, "domainSeparator": "0xf6f32572ffb2468f8a596c275105b095b673364ba8addafb6bf0a39f910e64d1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x111111111117dc0aa78b770fa6a738034120c302": {"kind": "permit", "domain": {"name": "1INCH Token", "version": "1", "chainId": 1, "verifyingContract": "0x111111111117dc0aa78b770fa6a738034120c302"}, "domainSeparator": "0xa9e572d7c103a339987674ba85d3bf220c2d3b50e12faa30581b80b4cd573427", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Cover Protocol Governance Token", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x7e876bc52a178a59a7c85f7d258e331be2a2f82627cefe5b877d2d3de362996d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "POW BTC-35W/T", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xc789e551a95f74165c4920d42ee28d474de47ddf9ec4874f03f680da86b6a739", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Opium Governance Token", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x926ae2474d393557d29cd64e38004eb99954b9c43535b9e966d9d7eaf5b3b7c6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Bridge Mutual", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x105463d47fd1759b5b1a315ae01e4f6027eb3587d38e1ebe79f2525245543d49", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0000000000095413afc295d19edeb1ad7b71c952": {"kind": "permit", "domain": {"name": "Tokenlon", "version": "1", "chainId": 1, "verifyingContract": "0x0000000000095413afc295d19edeb1ad7b71c952"}, "domainSeparator": "0x21fd8d2c87a18791598e51ec3986e4e9f57ac214dcba8437d43a18e766124388", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x297d33e17e61c2ddd812389c2105193f8348188a": {"kind": "permit", "domain": {"name": "Strudel Finance", "version": "1", "chainId": 1, "verifyingContract": "0x297d33e17e61c2ddd812389c2105193f8348188a"}, "domainSeparator": "0x81b424ea23987652e9a1231a3979d55b104b776361a5836c267d4f25bd532afa", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6e9730ecffbed43fd876a264c982e254ef05a0de": {"kind": "permit", "domain": {"name": "Nord Token", "version": "1", "chainId": 1, "verifyingContract": "0x6e9730ecffbed43fd876a264c982e254ef05a0de"}, "domainSeparator": "0x648e77fc0975e9aa3b0c8e77b09c5530f35ada4f3abda105e18b5a1abc7c00b0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x31c8eacbffdd875c74b94b077895bd78cf1e64a3": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "chainId": 1, "verifyingContract": "0x31c8eacbffdd875c74b94b077895bd78cf1e64a3"}, "domainSeparator": "0x79d3a808e84162cd15d680d4a79379672b88b7c654977c5cf347d47934b305af", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9b99cca871be05119b2012fd4474731dd653febe": {"kind": "permit", "domain": {"name": "Antimatter.Finance Governance Token", "chainId": 1, "verifyingContract": "0x9b99cca871be05119b2012fd4474731dd653febe"}, "domainSeparator": "0x8be6b6b94ac9993f14edb8b694f618abc810fb8711dba989aa9b739405c8d6fa", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2aeccb42482cc64e087b6d2e5da39f5a7a7001f8": {"kind": "permit", "domain": {"name": "Ruler Protocol", "version": "1", "chainId": 1, "verifyingContract": "0x2aeccb42482cc64e087b6d2e5da39f5a7a7001f8"}, "domainSeparator": "0x264685d7ca850d39e481734f7e3f079eaae91fc6a3fdebc982eb55c7c4053cd3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x888888435fde8e7d4c54cab67f206e4199454c60": {"kind": "permit", "domain": {"name": "DFX Token", "version": "1", "chainId": 1, "verifyingContract": "0x888888435fde8e7d4c54cab67f206e4199454c60"}, "domainSeparator": "0x5526f708a94cc75e371018126755c21938b15dcfdf938f9df34840c5605a13e0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa8b61cff52564758a204f841e636265bebc8db9b": {"kind": "permit", "domain": {"name": "Yield Protocol", "version": "1", "chainId": 1, "verifyingContract": "0xa8b61cff52564758a204f841e636265bebc8db9b"}, "domainSeparator": "0x83c9c5fb1e38e9c78f46cbc2d55e3f4bd762b978f42e2fe8c36e6537df84264f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcd2828fc4d8e8a0ede91bb38cf64b1a81de65bf6": {"kind": "permit", "domain": {"name": "OddzToken", "version": "1", "chainId": 1, "verifyingContract": "0xcd2828fc4d8e8a0ede91bb38cf64b1a81de65bf6"}, "domainSeparator": "0x4012263a3ca509812358b6f2d3e40b41d2080883335bcefb66a74bf441316ecc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xceb286c9604c542d3cc08b41aa6c9675b078a832": {"kind": "permit", "domain": {"name": "Vortex DeFi", "version": "1", "chainId": 1, "verifyingContract": "0xceb286c9604c542d3cc08b41aa6c9675b078a832"}, "domainSeparator": "0xf8e455c84e824a7af23ac118c64b90121bff475b3590ca3dd243cb3cc7c5bdd5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x956f47f50a910163d8bf957cf5846d573e7f87ca": {"kind": "permit", "domain": {"name": "Fei USD", "version": "1", "chainId": 1, "verifyingContract": "0x956f47f50a910163d8bf957cf5846d573e7f87ca"}, "domainSeparator": "0x0ff37d1dc9a7d6539faa7634e8848d1d0bb7ec77e2bcb30d8982e62e6e2d5914", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x88acdd2a6425c3faae4bc9650fd7e27e0bebb7ab": {"kind": "permit", "domain": {"name": "Alchemist", "version": "1", "chainId": 1, "verifyingContract": "0x88acdd2a6425c3faae4bc9650fd7e27e0bebb7ab"}, "domainSeparator": "0x561d3e62b3d53d038b3a5c43708c131dd451bc4c423b8b1a01391d9df54d3e32", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc477d038d5420c6a9e0b031712f61c5120090de9": {"kind": "permit", "domain": {"name": "<PERSON>son <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xc477d038d5420c6a9e0b031712f61c5120090de9"}, "domainSeparator": "0x4eb7f6b5b08c8d92f4e7c4d764c8b3785e9f658b76ba052f2e5ade1adfd9f97c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x48c3399719b582dd63eb5aadf12a40b4c3f52fa2": {"kind": "permit", "domain": {"name": "StakeWise", "version": "1", "chainId": 1, "verifyingContract": "0x48c3399719b582dd63eb5aadf12a40b4c3f52fa2"}, "domainSeparator": "0x94060d05b793159a4dcaf641945e580c54bdd60ca8c461ee95ab35f23b9c7813", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf16e81dce15b08f326220742020379b855b87df9": {"kind": "permit", "domain": {"name": "IceToken", "version": "1", "chainId": 1, "verifyingContract": "0xf16e81dce15b08f326220742020379b855b87df9"}, "domainSeparator": "0xfb7f85aa43ae37b6166f3318d36625bc9e267b6794d97973d03c700eb7d5c991", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5a666c7d92e5fa7edcb6390e4efd6d0cdd69cf37": {"kind": "permit", "domain": {"name": "UnmarshalToken", "version": "1", "chainId": 1, "verifyingContract": "0x5a666c7d92e5fa7edcb6390e4efd6d0cdd69cf37"}, "domainSeparator": "0xb53d0a61f3982ab12353ef58d331bfe5cabade7f2f505aa56f3d7f4f0a4b47c0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x90de74265a416e1393a450752175aed98fe11517": {"kind": "permit", "domain": {"name": "Unlock Discount Token", "version": "1", "chainId": 1, "verifyingContract": "0x90de74265a416e1393a450752175aed98fe11517"}, "domainSeparator": "0x7cbc02de6ee5b9c8ebba49207299c818ef648edff1a7ee06d5fa7c983a0b5fb3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x677ddbd918637e5f2c79e164d402454de7da8619": {"kind": "permit", "domain": {"name": "VUSD", "version": "1", "chainId": 1, "verifyingContract": "0x677ddbd918637e5f2c79e164d402454de7da8619"}, "domainSeparator": "0x99c5f819631b542be10b06242a53f20d0438b16c8d49d160e3ba469f86c03fa5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9695e0114e12c0d3a3636fab5a18e6b737529023": {"kind": "permit", "domain": {"name": "DFYN Token", "version": "1", "chainId": 1, "verifyingContract": "0x9695e0114e12c0d3a3636fab5a18e6b737529023"}, "domainSeparator": "0xa02e5e51d9d08bccf987f010f319e2fcb0fbc0d0f109a307393b65c1f841082b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1321f1f1aa541a56c31682c57b80ecfccd9bb288": {"kind": "permit", "domain": {"name": "ARCx Governance Token", "version": "1", "chainId": 1, "verifyingContract": "0x1321f1f1aa541a56c31682c57b80ecfccd9bb288"}, "domainSeparator": "0xcce3052d4b60aab1a5f34c3677254385b6cf46ca667d76aee93ba0f7544344a6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdddddd4301a082e62e84e43f474f044423921918": {"kind": "permit", "domain": {"name": "DeversiFi Token", "version": "1", "chainId": 1, "verifyingContract": "0xdddddd4301a082e62e84e43f474f044423921918"}, "domainSeparator": "0xac5fc81e406a4a34492a6fe0a8671fd9b6eb7cd6c82edf321ae31da883e26106", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x19042021329fddcfbea5f934fb5b2670c91f7d20": {"kind": "permit", "domain": {"name": "Take My Mu<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x19042021329fddcfbea5f934fb5b2670c91f7d20"}, "domainSeparator": "0x3ff2073c45593f5a78f9f3717a3d9339e4ada61370a403b516bfcf6c996c67b1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Wrapped Centrifuge", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x2499850519cbff0db3b29c5d1622d0a312fff06fe5d709905f78d542f189838f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Wrapped liquid staked Ether 2.0", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xd4a8ff90a402dc7d4fcbf60f5488291263c743ccff180e139f47d139cedfd5fe", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Merit Circle", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x8cb3d457fd6565aecb885b83391a86208b0b5099d6a71c32604ade76c4b39d71", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Ethereum Name Service", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x3172a7257acc6467eb985cfb1d52917228ac18e308bae31d65407e0983bd10a2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "RBX", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x288ae3a0a9fc5d1473e61cc5505b8cb9353a7071cc6cfa0c835d851b99727905", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Gas DAO", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xdfe9fbbbc22c19ae011386e5c70d40dc2662e9db52bcd18e9c4c361479be0d97", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa36fdbbae3c9d55a1d67ee5821d53b50b63a1ab9": {"kind": "permit", "domain": {"name": "Tempus", "version": "1", "chainId": 1, "verifyingContract": "0xa36fdbbae3c9d55a1d67ee5821d53b50b63a1ab9"}, "domainSeparator": "0x6d388172c00d8a21de12ddd9e635c59705fe2e43580de37234a69adcde1b1e58", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0de05f6447ab4d22c8827449ee4ba2d5c288379b": {"kind": "permit", "domain": {"name": "<PERSON>oki <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x0de05f6447ab4d22c8827449ee4ba2d5c288379b"}, "domainSeparator": "0x26034c380c6b41e6b089f9820e8a2ccbc11ba18ef2554000c27d25b8a1955e54", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1a7e4e63778b4f12a199c062f3efdd288afcbce8": {"kind": "permit", "domain": {"name": "agEUR", "version": "1", "chainId": 1, "verifyingContract": "0x1a7e4e63778b4f12a199c062f3efdd288afcbce8"}, "domainSeparator": "0x7cd562791c8802693be3d732713fdd807428117218268db838898f3a5806398e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x31429d1856ad1377a8a0079410b297e1a9e214c2": {"kind": "permit", "domain": {"name": "ANGLE", "version": "1", "chainId": 1, "verifyingContract": "0x31429d1856ad1377a8a0079410b297e1a9e214c2"}, "domainSeparator": "0x9cad5e02a8741c12383dc59c6db20ab66a9d4483c44f856d3f0150ca4f012097", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd38bb40815d2b0c2d2c866e0c72c5728ffc76dd9": {"kind": "permit", "domain": {"name": "Symbiosis", "version": "1", "chainId": 1, "verifyingContract": "0xd38bb40815d2b0c2d2c866e0c72c5728ffc76dd9"}, "domainSeparator": "0x14be05e1f3605c03782bdc9efdd11512cfc5df6603057fc64d897d5a5a5199db", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0f2d719407fdbeff09d87557abb7232601fd9f29": {"kind": "permit", "domain": {"name": "Synapse", "version": "1", "chainId": 1, "verifyingContract": "0x0f2d719407fdbeff09d87557abb7232601fd9f29"}, "domainSeparator": "0x1c0964dd9146ebc8f14e32b23f36025e5fb5ab1a2bb2f231ba3420b5ec15d055", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa5f2211b9b8170f694421f2046281775e8468044": {"kind": "permit", "domain": {"name": "THORSwap Token", "version": "1", "chainId": 1, "verifyingContract": "0xa5f2211b9b8170f694421f2046281775e8468044"}, "domainSeparator": "0x48522d01f6eb41c2ab2b2e91e3f7ac311d3460a7a6ae46cafb91a59a78707470", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x64aa3364f17a4d01c6f1751fd97c2bd3d7e7f1d5": {"kind": "permit", "domain": {"name": "Olympus", "version": "1", "chainId": 1, "verifyingContract": "0x64aa3364f17a4d01c6f1751fd97c2bd3d7e7f1d5"}, "domainSeparator": "0xb86338c2ea64c0e4cc86f7224cf3207aaae6f51fd55aa8b31cf5b137cc777b7f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdef1ca1fb7fbcdc777520aa7f396b4e015f497ab": {"kind": "permit", "domain": {"name": "CoW Protocol Token", "version": "1", "chainId": 1, "verifyingContract": "0xdef1ca1fb7fbcdc777520aa7f396b4e015f497ab"}, "domainSeparator": "0x82b58cb6c4763c2e16f6ab9b7f31298f8c336f40e20275bee4874eaef35b26a7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7f280dac515121dcda3eac69eb4c13a52392cace": {"kind": "permit", "domain": {"name": "Fancy Games", "version": "1", "chainId": 1, "verifyingContract": "0x7f280dac515121dcda3eac69eb4c13a52392cace"}, "domainSeparator": "0xcfdbe30ee0bdde5cabe87a8ce73bd2df4f62b63d7e2cfa177e0cf027a32e692d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x299698b4b44bd6d023981a7317798dee12860834": {"kind": "permit", "domain": {"name": "New Frontier Presents", "version": "1", "chainId": 1, "verifyingContract": "0x299698b4b44bd6d023981a7317798dee12860834"}, "domainSeparator": "0x2fdb4605075b2569d42a1fdb61f7c112bb459894b35a305db6ce04fcaf6ff29a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8f693ca8d21b157107184d29d398a8d082b38b76": {"kind": "permit", "domain": {"name": "Streamr", "version": "1", "chainId": 1, "verifyingContract": "0x8f693ca8d21b157107184d29d398a8d082b38b76"}, "domainSeparator": "0xb9842fd8e5d534a9ec888e765a1504f110412c4d1371bf36a86e86d7a0d4bb8d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4cf89ca06ad997bc732dc876ed2a7f26a9e7f361": {"kind": "permit", "domain": {"name": "Mysterium", "version": "1", "chainId": 1, "verifyingContract": "0x4cf89ca06ad997bc732dc876ed2a7f26a9e7f361"}, "domainSeparator": "0x50eac8fa389c4c4e0f032306d95573a9a47272d5a7974afb0bc9e01698655184", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc5102fe9359fd9a28f877a67e36b0f050d81a3cc": {"kind": "permit", "domain": {"name": "Hop", "version": "1", "chainId": 1, "verifyingContract": "0xc5102fe9359fd9a28f877a67e36b0f050d81a3cc"}, "domainSeparator": "0x858635b48cb2099c770f4d18d85dbeac92bd8147b5c5ae08b4092c09862973dc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4b520c812e8430659fc9f12f6d0c39026c83588d": {"kind": "permit", "domain": {"name": "Decentral Games", "version": "1", "chainId": 1, "verifyingContract": "0x4b520c812e8430659fc9f12f6d0c39026c83588d"}, "domainSeparator": "0x1aa6872319b13feee34400f31687bd503b81ae50e6f7eb0854efb2846f2328a7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6bea7cfef803d1e3d5f7c0103f7ded065644e197": {"kind": "permit", "domain": {"name": "Gamma", "version": "1", "chainId": 1, "verifyingContract": "0x6bea7cfef803d1e3d5f7c0103f7ded065644e197"}, "domainSeparator": "0x6da7c03d7f834655ab565423cdd44318c01c20dbf3a288f667b1b5e429613425", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbea0000029ad1c77d3d5d23ba2d8893db9d1efab": {"kind": "permit", "domain": {"name": "<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xbea0000029ad1c77d3d5d23ba2d8893db9d1efab"}, "domainSeparator": "0x82d7f12cfe2cfa862de849445e206deaa0df4b95c140da4ef3d7855a687b9af1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x111111517e4929d3dcbdfa7cce55d30d4b6bc4d6": {"kind": "permit", "domain": {"name": "ICHI", "chainId": 1, "verifyingContract": "0x111111517e4929d3dcbdfa7cce55d30d4b6bc4d6"}, "domainSeparator": "0x6a6ee7fd927d333f5911d368dc7239f16487ff0b47ecd0db60766fe4162a47b6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf1dc500fde233a4055e25e5bbf516372bc4f6871": {"kind": "permit", "domain": {"name": "Saddle DAO", "version": "1", "chainId": 1, "verifyingContract": "0xf1dc500fde233a4055e25e5bbf516372bc4f6871"}, "domainSeparator": "0x1cb181da94a689090c7cbf8ad9bf3fafe7f24280b1335ea361187c66c3f31814", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd084944d3c05cd115c09d072b9f44ba3e0e45921": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0xd084944d3c05cd115c09d072b9f44ba3e0e45921"}, "domainSeparator": "0xbc2582a8f41198f1123c7d328eaf56de943f42bef629bc1e008a45d6be36d933", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb0b195aefa3650a6908f15cdac7d92f8a5791b0b": {"kind": "permit", "domain": {"name": "BOB", "version": "1", "chainId": 1, "verifyingContract": "0xb0b195aefa3650a6908f15cdac7d92f8a5791b0b"}, "domainSeparator": "0x89ae8e5c4b66ead9633eda9b816caf7be1b63c83da93250c795d803856f7c588", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x865377367054516e17014ccded1e7d814edc9ce4": {"kind": "permit", "domain": {"name": "Dola USD Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "0x865377367054516e17014ccded1e7d814edc9ce4"}, "domainSeparator": "0xdc655d8531d98f59dd4017030cb566baa82f92c921b34a78010cad1feb3a9969", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3af33bef05c2dcb3c7288b77fe1c8d2aeba4d789": {"kind": "permit", "domain": {"name": "Kromatika", "version": "1", "chainId": 1, "verifyingContract": "0x3af33bef05c2dcb3c7288b77fe1c8d2aeba4d789"}, "domainSeparator": "0x6ee7b396ac20f5e65dbabfdd8e95284b3bfc68a84d51028dd1a5d6647e5bbba0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x350d3f0f41b5b21f0e252fe2645ae9d55562150a": {"kind": "permit", "domain": {"name": "BananaClubToken", "version": "1", "chainId": 1, "verifyingContract": "0x350d3f0f41b5b21f0e252fe2645ae9d55562150a"}, "domainSeparator": "0x743c2c160af2db137f5a230195ffd97d2526afc19ee19a23a175c7bb3c3fade2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6e98f1fbd2bc9d827499e69f9bbb0a72bd5c9b2d": {"kind": "permit", "domain": {"name": "1COIN Network", "version": "1", "chainId": 1, "verifyingContract": "0x6e98f1fbd2bc9d827499e69f9bbb0a72bd5c9b2d"}, "domainSeparator": "0xf11d80cf5fbb443f345d7347838922c080d5ae8a608d4871fc54e846ca82c9d7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2596b971ee0de4532566c59fa394c0d29f21d224": {"kind": "permit", "domain": {"name": "2048", "version": "1", "chainId": 1, "verifyingContract": "0x2596b971ee0de4532566c59fa394c0d29f21d224"}, "domainSeparator": "0xb1303d700aad879880e798c51e9c8c0a47fb3bba1ddb59c969bd3e5e3551d9b2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2c9c19ce3b15ae77c6d80aec3c1194cfd6f7f3fa": {"kind": "permit", "domain": {"name": "2CrazyToken", "version": "1", "chainId": 1, "verifyingContract": "0x2c9c19ce3b15ae77c6d80aec3c1194cfd6f7f3fa"}, "domainSeparator": "0xcc8bf05ff86ef37e2de4e278840f586911532f7ce44aa7b6c439095f521ab79e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa1033592f20e579863986b5734ba1df9070c0363": {"kind": "permit", "domain": {"name": "<PERSON>u <PERSON>u", "version": "1", "chainId": 1, "verifyingContract": "0xa1033592f20e579863986b5734ba1df9070c0363"}, "domainSeparator": "0x20dcd5d5858efe01416ec3df9ad2cd43c1e2bdf18182ca467e4700fa55078726", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8a14897ea5f668f36671678593fae44ae23b39fb": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x8a14897ea5f668f36671678593fae44ae23b39fb"}, "domainSeparator": "0xeeb374f17dc83f4ca50260b860ca73691e79f3e121ced229eb708e7e54c2ff68", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf054aa5a8f8e0e016272b3429d579ebcd41ed799": {"kind": "permit", "domain": {"name": "Inu Inu Inu", "version": "1", "chainId": 1, "verifyingContract": "0xf054aa5a8f8e0e016272b3429d579ebcd41ed799"}, "domainSeparator": "0x935ad996ddb2736ac4f36180f4f2c4f899fe8bfe812feec216fe91b2d60677f3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xee62f72fb0cd61ced2e7a1ca56ea14ee6eae8452": {"kind": "permit", "domain": {"name": "A", "version": "1", "chainId": 1, "verifyingContract": "0xee62f72fb0cd61ced2e7a1ca56ea14ee6eae8452"}, "domainSeparator": "0x4ea2d85fb17e8682c6d01b8045a29dbaa8066318639a6444517bff776c40e3f0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8b4c7f7d03c4fab12c394a65f2fb6ba023e26c6a": {"kind": "permit", "domain": {"name": "SCHIZO DAO GOVERNANCE TOKEN", "version": "1", "chainId": 1, "verifyingContract": "0x8b4c7f7d03c4fab12c394a65f2fb6ba023e26c6a"}, "domainSeparator": "0xcaf4327d0b69b941b811ff78341afc577e2099bd626551f9b960a064230ea1d8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1934e252f840aa98dfce2b6205b3e45c41aef830": {"kind": "permit", "domain": {"name": "₡ABIN", "version": "1", "chainId": 1, "verifyingContract": "0x1934e252f840aa98dfce2b6205b3e45c41aef830"}, "domainSeparator": "0xfdf5e6e05306443edebb8cb90c510f52b575904519ae8877fd0a75232ad3b842", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "ARTCOIN", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0047c18bdd9a54e7e585a9218b8b9ac1250c49ce0948e5f1ef439393e2b40637", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Asymmetry Crypto Capital", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x4714543a126fddf7555bbab743a9ac26bb3c0495c16c6e7e834715ed5fee10bb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Arable Protocol", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xda7c78ac75bf7788f0eb4024f883ca16c4f7521a785bb93379eb08580c7a25c4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "AdEx <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xf539934bb54e961f5a088c74c5bdbe98283513ba875def9defdb5644dfc2951b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb3ed706b564bba9cab64042f4e1b391be7bebce5": {"kind": "permit", "domain": {"name": "Augmented Finance", "version": "1", "chainId": 1, "verifyingContract": "0xb3ed706b564bba9cab64042f4e1b391be7bebce5"}, "domainSeparator": "0x39833f804270cdfddc3db97bb574798ffcb99c251091969aff2b8fdc0bfed0ae", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x80c24111d3f9e1ffecf3b41b9ca68e60a890866b": {"kind": "permit", "domain": {"name": "Aggregated Finance", "version": "1", "chainId": 1, "verifyingContract": "0x80c24111d3f9e1ffecf3b41b9ca68e60a890866b"}, "domainSeparator": "0x63864db2f0726809e77e5514020fd3cd3f09a5677709445efa93aa3340c600c8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4d0f56d728c5232ab07faa0bdcba23670a35451f": {"kind": "permit", "domain": {"name": "Aggregated Finance", "version": "1", "chainId": 1, "verifyingContract": "0x4d0f56d728c5232ab07faa0bdcba23670a35451f"}, "domainSeparator": "0x55929cf1286bc6ac10782d639524ce7b59a5f12e4d385b4f044e3f5891a5750d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x667fd83e24ca1d935d36717d305d54fa0cac991c": {"kind": "permit", "domain": {"name": "Collector Coin", "version": "1", "chainId": 1, "verifyingContract": "0x667fd83e24ca1d935d36717d305d54fa0cac991c"}, "domainSeparator": "0x4dc60b9499fa3b3176ac946f3dc52c1fb67915468f52ece6448b4ea686b1af0d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc0134b5b924c2fca106efb33c45446c466fbe03e": {"kind": "permit", "domain": {"name": "aleph.im", "version": "1", "chainId": 1, "verifyingContract": "0xc0134b5b924c2fca106efb33c45446c466fbe03e"}, "domainSeparator": "0xb8f8ca4a30e9b1dea934be3608a6ff75c19142098541747130a41cea14577027", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1e5193ccc53f25638aa22a940af899b692e10b09": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x1e5193ccc53f25638aa22a940af899b692e10b09"}, "domainSeparator": "0xf4423133cc88f6dc7bf85bcb23e773d1de553f5e7bda8520e1549f73895bced2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x09a8fb018326a4647488ccb340c211f510a466d3": {"kind": "permit", "domain": {"name": "ALPHA", "version": "1", "chainId": 1, "verifyingContract": "0x09a8fb018326a4647488ccb340c211f510a466d3"}, "domainSeparator": "0x3efa5b2471739d7a3ffc6b9ba3033c28cdbfeb3795b3cd265abee131a440d338", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x507586012a126421c3669a64b8393fffa9c44462": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x507586012a126421c3669a64b8393fffa9c44462"}, "domainSeparator": "0x75c390be1c67941451e6284fd03546cbc7b6385a0b9c9247fcd081c7d1e9370f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4332f8a38f14bd3d8d1553af27d7c7ac6c27278d": {"kind": "permit", "domain": {"name": "Ape Finance", "version": "1", "chainId": 1, "verifyingContract": "0x4332f8a38f14bd3d8d1553af27d7c7ac6c27278d"}, "domainSeparator": "0x503e3b0ab1c619345d8803df505da6dffc1355fd60b94662a83b850dd13ec006", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf04847b93612ee9e7327f31906e7710b252ba4a1": {"kind": "permit", "domain": {"name": "Aphra Finance", "version": "1", "chainId": 1, "verifyingContract": "0xf04847b93612ee9e7327f31906e7710b252ba4a1"}, "domainSeparator": "0xe29ae09f38a46745b7c93cbcf550b32719013bf604ab2f342c793118f3f2b8c4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe6483e957b6e43bd780a8f98372303e6f11699fd": {"kind": "permit", "domain": {"name": "AptosAI", "version": "1", "chainId": 1, "verifyingContract": "0xe6483e957b6e43bd780a8f98372303e6f11699fd"}, "domainSeparator": "0x7ec159a2c3f72c74fa2f24b5fb8dab22d6bc9205b6398696a7dcbbc364cca3e3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x51bde723d5cf0ce453870ab8e0faa17e228adf1b": {"kind": "permit", "domain": {"name": "A<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x51bde723d5cf0ce453870ab8e0faa17e228adf1b"}, "domainSeparator": "0x44e635cbcdeaeed174e3b4bc88e6b28bd2d82960d98f86dd68b319de8ce6c131", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcc665390b03c5d324d8faf81c15ecee29a73bcb4": {"kind": "permit", "domain": {"name": "ChainSwap.com Governance Token", "chainId": 1, "verifyingContract": "0xcc665390b03c5d324d8faf81c15ecee29a73bcb4"}, "domainSeparator": "0xfd3fbc067b94e09c09485618f660326ec82f133ed9e0a5bda8a976f99b5c6951", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xec3507c639231df837ec0769e61513ac4a6c9fb3": {"kind": "permit", "domain": {"name": "Alien Space Cat Club", "version": "1", "chainId": 1, "verifyingContract": "0xec3507c639231df837ec0769e61513ac4a6c9fb3"}, "domainSeparator": "0x222c9df3766845d6b4d0bf16a860c34bc1b0d086798d493d19c8b1b0eea44946", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0dc5189ec8cde5732a01f0f592e927b304370551": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x0dc5189ec8cde5732a01f0f592e927b304370551"}, "domainSeparator": "0xe903c184e520931d3f978ec2238f41feda76e9835f9cb44d3d5362785d06b2e9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x56694577564fdd577a0abb20fe95c1e2756c2a11": {"kind": "permit", "domain": {"name": "Wrapped AdaSwap", "version": "1", "chainId": 1, "verifyingContract": "0x56694577564fdd577a0abb20fe95c1e2756c2a11"}, "domainSeparator": "0x39683ea4b65eb6cda949dd48ea870fc59001c52b78a40cea2cc443d9cf448543", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf98f61a89e1a2c521229d1395d5fbc39a4e58a9a": {"kind": "permit", "domain": {"name": "Aureus Coin", "version": "1", "chainId": 1, "verifyingContract": "0xf98f61a89e1a2c521229d1395d5fbc39a4e58a9a"}, "domainSeparator": "0xc6baeadb0ce1daca7a05d051b1e8cf3aa7453dca6b283f8c1a0831aa4ce498a3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x30ac8317dfb0ab4263cd8db1c4f10749911b126c": {"kind": "permit", "domain": {"name": "AxeDAO", "version": "1", "chainId": 1, "verifyingContract": "0x30ac8317dfb0ab4263cd8db1c4f10749911b126c"}, "domainSeparator": "0x534d056f39c1a8775c9e15f3fc9c43daa91efca4fbd333d62aeb7461191630c4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x467719ad09025fcc6cf6f8311755809d45a5e5f3": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x467719ad09025fcc6cf6f8311755809d45a5e5f3"}, "domainSeparator": "0xc2ef7c4f9bcedc4065bf96c4c353adec4d6a3fa9ce6507c4922f082e950b9921", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Axelar Wrapped ATOM", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x6126816bb537b4333ed77f482170cebe8e44d3380befaac68033e190159a66e5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Balancer 80 BAL 20 WETH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1575652e6425492e8825412c139a1146413139599b4282f13a90279d1fe0d79f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Backrooms", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x9d55a637586d66d1a46e989873cfe81687f05259a155ae34c53f62623e576642", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "a COIN called BEAST ", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xe4155921c2b459525f4bcd40973c392b353df46f8343f6de08c20265fd66fa86", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3703f712945f8111fe2c5f9ae155a52560e2065c": {"kind": "permit", "domain": {"name": "Bamboonium", "version": "1", "chainId": 1, "verifyingContract": "0x3703f712945f8111fe2c5f9ae155a52560e2065c"}, "domainSeparator": "0x4b426ced676b6b308c4cef988cdb77b27bf502448af6a878fc66dc9da51ddf0c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x14079e63e492bb7df6aeb4fe4e48cb4f18f3488a": {"kind": "permit", "domain": {"name": "Dogechain Fan Club Governance Token", "version": "1", "chainId": 1, "verifyingContract": "0x14079e63e492bb7df6aeb4fe4e48cb4f18f3488a"}, "domainSeparator": "0x9d2cfb1d7e88df4d594bfaf299126a6e3e733565b9ca9bc4b13fe46ab984a23b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5646077e2779a4e8607a99257bd76f627a262d86": {"kind": "permit", "domain": {"name": "ATBASH", "version": "1", "chainId": 1, "verifyingContract": "0x5646077e2779a4e8607a99257bd76f627a262d86"}, "domainSeparator": "0x84eb57d04e863ee6bfc10eb028397a2fb0280dc633f9d70ae459020a51f4fab1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0424af689f9155fa71d77e532ba847a0e2e6ae3a": {"kind": "permit", "domain": {"name": "Baskets.Finance", "version": "1", "chainId": 1, "verifyingContract": "0x0424af689f9155fa71d77e532ba847a0e2e6ae3a"}, "domainSeparator": "0x2bb9808d0b579e881953c4c4bfe1eb05a490d9b0705a3d6e23a35cbe0cf1cf8b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xae37d54ae477268b9997d4161b96b8200755935c": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON> (DAI)", "version": "1", "chainId": 1, "verifyingContract": "0xae37d54ae477268b9997d4161b96b8200755935c"}, "domainSeparator": "0x5ae2ff0a19a6bd565552591bdcd0e2cc3e1db5dd7fb1b47485d06df13e50ebee", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x804cdb9116a10bb78768d3252355a1b18067bf8f": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON> (DAI)", "version": "1", "chainId": 1, "verifyingContract": "0x804cdb9116a10bb78768d3252355a1b18067bf8f"}, "domainSeparator": "0x496bbc66e025a5dd291b54dd74bb2cdaf1f837de464ac9caafd331291cf2980d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa13a9247ea42d743238089903570127dda72fe44": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON> Boosted StablePool", "version": "1", "chainId": 1, "verifyingContract": "0xa13a9247ea42d743238089903570127dda72fe44"}, "domainSeparator": "0x7811f63ece0ec0c6ca1d7094b75a1b2f0c3dfb9d3e4c84c5cff4de181516a94e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7b50775383d3d6f0215a8f290f2c9e2eebbeceb2": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON> Boosted StablePool (USD)", "version": "1", "chainId": 1, "verifyingContract": "0x7b50775383d3d6f0215a8f290f2c9e2eebbeceb2"}, "domainSeparator": "0x7681ff5ee633f4e612bf4d077007b3a5c3bb0b753fc6fe21712d144702af420f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x82698aecc9e28e9bb27608bd52cf57f704bd1b83": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON>ed <PERSON> (USDC)", "version": "1", "chainId": 1, "verifyingContract": "0x82698aecc9e28e9bb27608bd52cf57f704bd1b83"}, "domainSeparator": "0x58da79a6d4e7fa8ab2f95d3b12d721f67d451a2c0c9d309012e3c99b062dd86b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9210f1204b5a24742eba12f710636d76240df3d0": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON>ed <PERSON> (USDC)", "version": "1", "chainId": 1, "verifyingContract": "0x9210f1204b5a24742eba12f710636d76240df3d0"}, "domainSeparator": "0x182269b7b9084b7cb03c0715dbedd00eb6debbe8fe922ad411dff76f6c8ce6c8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2f4eb100552ef93840d5adc30560e5513dfffacb": {"kind": "permit", "domain": {"name": "Balancer <PERSON><PERSON>osted Pool (USDT)", "version": "1", "chainId": 1, "verifyingContract": "0x2f4eb100552ef93840d5adc30560e5513dfffacb"}, "domainSeparator": "0xdaaa2d34f1282c91c2d86fea654faf2b346a7c2dc0222c99398acefe8f5406b0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "BBS", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x6ad0dead121eda6f88c1edd63f3bbecac32d72930212951a10a1f293b3536f4f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "BMining POW BTC-50W", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0fabc23fa8ddd2dd7cb0efdde54cca3934afd73f33d38873e2eae4340788deb0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "BTU Incentivized DAI", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x16c3b7e254c959d26b549c61e8c761491f3f33329680c14b1b082e9ccf52aaf7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Bear", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x8c073a7f6bd091fc3cca24c37c66a02878906b3c557cc759e7f2b13fe8fe8927", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8f081eb884fd47b79536d28e2dd9d4886773f783": {"kind": "permit", "domain": {"name": "be<PERSON><PERSON>n", "version": "1", "chainId": 1, "verifyingContract": "0x8f081eb884fd47b79536d28e2dd9d4886773f783"}, "domainSeparator": "0xdf81bf5dc3646e708b783f1a8ce9e15ea74d28cb45963eb2f791a04073504eee", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0d02755a5700414b26ff040e1de35d337df56218": {"kind": "permit", "domain": {"name": "Bend Token", "version": "1", "chainId": 1, "verifyingContract": "0x0d02755a5700414b26ff040e1de35d337df56218"}, "domainSeparator": "0xe5065f03e0636113a6c27099c56347f166e35c087b4315605294503516da1397", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "BergDAO", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0005128d37e188f368609766a65e3f4721ad68a80b537418787e5b21e684db6c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "BMining POW ETH-30W", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xd7d64ab629573864d3e4175940bf93f57ecd80edb3627b5e79a161ab13eabd19", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "BIGCAP", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xc279b054d3c556b447952987bd945afe32977368782d264289bc5629b025e62f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "BIG Token test 6.1", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xad929747875e50f877203ade183b84cdac809d5dc3143a5a21511036dcaf1505", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb97b0de16dd20effb0d6715f4cbac37f5e1bf3d5": {"kind": "permit", "domain": {"name": "BIG Token test 8.1", "version": "1", "chainId": 1, "verifyingContract": "0xb97b0de16dd20effb0d6715f4cbac37f5e1bf3d5"}, "domainSeparator": "0x4c4da902e8006ae645057e315f8309f2782016eaeb03b8bb56168876fcc92774", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x15ee120fd69bec86c1d38502299af7366a41d1a6": {"kind": "permit", "domain": {"name": "BitANT", "version": "1", "chainId": 1, "verifyingContract": "0x15ee120fd69bec86c1d38502299af7366a41d1a6"}, "domainSeparator": "0x24b2515afbad0bc63e8021cc59fb96fcd9079259e9f5d5611609127995dd2032", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4efc1fbed6598551c30a830758244ecc9706dfb4": {"kind": "permit", "domain": {"name": "Burned Bytes DAO", "version": "1", "chainId": 1, "verifyingContract": "0x4efc1fbed6598551c30a830758244ecc9706dfb4"}, "domainSeparator": "0x9d6f1c7b45ec178b3fe0dc9d846101914aa563d6c1738b3e6b5de0686debac81", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x090f572496ce30a4ffc62a0a9f2a7540f2a997b5": {"kind": "permit", "domain": {"name": "BlahToken", "version": "1", "chainId": 1, "verifyingContract": "0x090f572496ce30a4ffc62a0a9f2a7540f2a997b5"}, "domainSeparator": "0x906848b54cbb865cb6d24d16eba150bb52014b8ac4d156bfd4d0da65fe8a637d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x614d7f40701132e25fe6fc17801fbd34212d2eda": {"kind": "permit", "domain": {"name": "SafeBLAST", "version": "1", "chainId": 1, "verifyingContract": "0x614d7f40701132e25fe6fc17801fbd34212d2eda"}, "domainSeparator": "0x31ec117b1cfd50b37e65ff7ab8cacc7bd42eba6eb9e294aaa67b4042d8f9fcdc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8bcd06492416a749c9369009b3429861b7f27f6e": {"kind": "permit", "domain": {"name": "BlackHat", "version": "1", "chainId": 1, "verifyingContract": "0x8bcd06492416a749c9369009b3429861b7f27f6e"}, "domainSeparator": "0x870467f82a8f78d87a02eb5aa667aa4642fa8302689e6882f3da203a72d870cd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x48983d42ddc25e98b738c231c022e22ef0a851cc": {"kind": "permit", "domain": {"name": "BlackDAO", "version": "1", "chainId": 1, "verifyingContract": "0x48983d42ddc25e98b738c231c022e22ef0a851cc"}, "domainSeparator": "0x8bcf7a0a5731b3227a0304ca3ba0b6a7067e2c20239203b98cc98969490eef77", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe7da1d8327ae1f320bbc23ca119b02ad3f85dceb": {"kind": "permit", "domain": {"name": "BlackDAO", "version": "1", "chainId": 1, "verifyingContract": "0xe7da1d8327ae1f320bbc23ca119b02ad3f85dceb"}, "domainSeparator": "0x38e60886443446bef04eba6c1abf0519e6691fefb43c83adc94439d8f117b4bd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xff4cb3800f257c86fe0cec6c4a30136395994496": {"kind": "permit", "domain": {"name": "Blood", "version": "1", "chainId": 1, "verifyingContract": "0xff4cb3800f257c86fe0cec6c4a30136395994496"}, "domainSeparator": "0x6079dab3151c56b766e55c96e7a68a0643fe46f39a8865f3013ed8f19f071321", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe7b40046a1f6f5561a6edf329f61874f3fdcd3b1": {"kind": "permit", "domain": {"name": "Ballast", "version": "1", "chainId": 1, "verifyingContract": "0xe7b40046a1f6f5561a6edf329f61874f3fdcd3b1"}, "domainSeparator": "0xffb86d47a654f41ab29de591010296f351445b33ac14deb27f188e4efc807f2c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb2d973b457f60c29998d5d377e78ea2fcd03abca": {"kind": "permit", "domain": {"name": "Twitter Blue", "version": "1", "chainId": 1, "verifyingContract": "0xb2d973b457f60c29998d5d377e78ea2fcd03abca"}, "domainSeparator": "0x99d5e4a0c735653eb416dd231c55477078dddca4376e7fd0337df0edf1b61bac", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x42bbfa2e77757c645eeaad1655e0911a7553efbc": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x42bbfa2e77757c645eeaad1655e0911a7553efbc"}, "domainSeparator": "0xfe6b086dfda70641eb518d7abf7632e20f1b1046707672cb4df8eba7491d26f7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xda91e5547cba22e79d0c66bd9040aed90f0363c5": {"kind": "permit", "domain": {"name": "BOG", "version": "1", "chainId": 1, "verifyingContract": "0xda91e5547cba22e79d0c66bd9040aed90f0363c5"}, "domainSeparator": "0x0003707ba7433151675593df6a3c03ed320c3cc04ffa34e3b929ba44d35c7c9f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x55af5865807b196bd0197e0902746f31fbccfa58": {"kind": "permit", "domain": {"name": "SpookyToken", "version": "1", "chainId": 1, "verifyingContract": "0x55af5865807b196bd0197e0902746f31fbccfa58"}, "domainSeparator": "0x90e5f68bfe3412c445b47e3130e83c4de7e67d16c913645900620bbd98f7019a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0337fe811809a0aaf9b5d07945b39e473de4c46e": {"kind": "permit", "domain": {"name": "Boot Finance", "version": "1", "chainId": 1, "verifyingContract": "0x0337fe811809a0aaf9b5d07945b39e473de4c46e"}, "domainSeparator": "0xf39eefe998163f3f5457f5516f17ccfae05fc06317451f5eb59752ac98f7f7f0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6facc8ce4f55e543c9e1296cab4f7805264c1c73": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x6facc8ce4f55e543c9e1296cab4f7805264c1c73"}, "domainSeparator": "0x299b95e0be781dd06daeeb8bbe1a59cb73dfb54e6482b6cd70ec996d4372ab64", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbdcdbd0d59f71aab0724e5fd4b15bceef60898e9": {"kind": "permit", "domain": {"name": "Bot Wars", "version": "1", "chainId": 1, "verifyingContract": "0xbdcdbd0d59f71aab0724e5fd4b15bceef60898e9"}, "domainSeparator": "0xeffaae8739019fc8f25dfef928e6b66d874b09b4c354ff19b678dc53d4b44e03", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x94e9eb8b5ab9fd6b9ea3169d55ffade62a01702e": {"kind": "permit", "domain": {"name": "BreederDAO", "version": "1", "chainId": 1, "verifyingContract": "0x94e9eb8b5ab9fd6b9ea3169d55ffade62a01702e"}, "domainSeparator": "0x1d9c6e4646886902acc0ec9da0d0cebb8a0206a931bb001bb18f861eff15d82f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd4e12b224c316664ebb647f69abc1fb8bb2697c7": {"kind": "permit", "domain": {"name": "Bribe Token", "version": "1", "chainId": 1, "verifyingContract": "0xd4e12b224c316664ebb647f69abc1fb8bb2697c7"}, "domainSeparator": "0xfa7daaa62c1c7bbc327e2c06503a50d5b49d3f02c943397e93635753554e33b4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbeab712832112bd7664226db7cd025b153d3af55": {"kind": "permit", "domain": {"name": "Bright Union", "version": "1", "chainId": 1, "verifyingContract": "0xbeab712832112bd7664226db7cd025b153d3af55"}, "domainSeparator": "0xecfb68ff06634b7d337013adb0a14e605472ce9062b1fca69a9f577006f64968", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xae561dc54ed96c3d55103beb17e181a44db958c0": {"kind": "permit", "domain": {"name": "Berry INU", "version": "1", "chainId": 1, "verifyingContract": "0xae561dc54ed96c3d55103beb17e181a44db958c0"}, "domainSeparator": "0xc8b03f81473bb1ac860f6f44eb56f91c5c557ee60f9b2f863c80c44a506f6a43", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x69570f3e84f51ea70b7b68055c8d667e77735a25": {"kind": "permit", "domain": {"name": "Betswap.gg", "version": "1", "chainId": 1, "verifyingContract": "0x69570f3e84f51ea70b7b68055c8d667e77735a25"}, "domainSeparator": "0x246895e5e110b9914252559d275afcc61da5e23682daa9a72a5dde739a26ec27", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x32e6c34cd57087abbd59b5a4aecc4cb495924356": {"kind": "permit", "domain": {"name": "BitBase", "version": "1", "chainId": 1, "verifyingContract": "0x32e6c34cd57087abbd59b5a4aecc4cb495924356"}, "domainSeparator": "0xb0474576af9bb3c7616204e99a8d002554d7a80df03e9266038b822d6914a884", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfbdd194376de19a88118e84e279b977f165d01b8": {"kind": "permit", "domain": {"name": "Bat True Dollar", "version": "1", "chainId": 1, "verifyingContract": "0xfbdd194376de19a88118e84e279b977f165d01b8"}, "domainSeparator": "0x5cb60418779d824ea4f7995b4bd6d6f999db4b9ea1bc818c2fc4252e7776dd6d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc55126051b22ebb829d00368f4b12bde432de5da": {"kind": "permit", "domain": {"name": "BTRFLY", "version": "1", "chainId": 1, "verifyingContract": "0xc55126051b22ebb829d00368f4b12bde432de5da"}, "domainSeparator": "0x0cc080a98373cfc8d8d2ee93708d158c7bf50a4f8541b39b84836f0047850013", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc0d4ceb216b3ba9c3701b291766fdcba977cec3a": {"kind": "permit", "domain": {"name": "BTRFLY", "version": "1", "chainId": 1, "verifyingContract": "0xc0d4ceb216b3ba9c3701b291766fdcba977cec3a"}, "domainSeparator": "0x67f9575decadc49198bb88d111501a621d6b8147cdc6a1de98a02806ef64832e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x355cd8b9fc9a514a1d3ff7aaabb8a9ce2fb5e696": {"kind": "permit", "domain": {"name": "BullRun", "version": "1", "chainId": 1, "verifyingContract": "0x355cd8b9fc9a514a1d3ff7aaabb8a9ce2fb5e696"}, "domainSeparator": "0x48d2c9936bbb04383454968d9e4ed982be2af1b922bed621a77143919803f3be", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x785c34312dfa6b74f6f1829f79ade39042222168": {"kind": "permit", "domain": {"name": "BUMP", "version": "1", "chainId": 1, "verifyingContract": "0x785c34312dfa6b74f6f1829f79ade39042222168"}, "domainSeparator": "0x7b50e44bd1aaeb1980970cefded86bdedfd1bbb043416f5f910116cfa1e68f59", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xee31e8597d972cfd136042719bdbc8b24020f0d0": {"kind": "permit", "domain": {"name": "Bustad Coin", "version": "1", "chainId": 1, "verifyingContract": "0xee31e8597d972cfd136042719bdbc8b24020f0d0"}, "domainSeparator": "0x5e732a4b937fae5f0f0e495962cdec23f3302ff214c5ae362d388d3dcb352c86", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2734afeab5f94883dd6ee40acd0d42de978a443c": {"kind": "permit", "domain": {"name": "Blackrock USD", "version": "1", "chainId": 1, "verifyingContract": "0x2734afeab5f94883dd6ee40acd0d42de978a443c"}, "domainSeparator": "0x2a64a1cfa50c0a5eda00d12ec9047900eb903758aa5488d08c045eb7154e6b55", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcadc0acd4b445166f12d2c07eac6e2544fbe2eef": {"kind": "permit", "domain": {"name": "CAD Coin", "version": "2", "chainId": 1, "verifyingContract": "0xcadc0acd4b445166f12d2c07eac6e2544fbe2eef"}, "domainSeparator": "0xf693f971fed6fa392e483549e179d9d104db8a7680ad9a6b15836382f8f94269", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "CanvasToken", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x5e65c446e51a3b905ad4d2f4ac1bf29cfb7b562c9e8b2b36c1c8f171d8f2465d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "CryptoArt.Ai", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x74ab96aa97c24c914146377076ae1baf54bfc7d1e32d8dc8458cfa9366c9bbc8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Coinbase Wrapped Staked ETH", "version": "2", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x04f14019d5946f59c261cc47c80cce951ffd35ef52dbed0e2c02d646c9013541", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x81cdd9d40b2294de555fc1aa4239e11b38293fae4c8827c46e7dedfa153a7171", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "CFL365 Finance", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x268d8e927bde9d19e00d7594aae8a543699788e108d85138934806458a3a10d3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbe62613d3abbf3d86f0806bcc12805b41e7e4428": {"kind": "permit", "domain": {"name": "ChainGain", "version": "1", "chainId": 1, "verifyingContract": "0xbe62613d3abbf3d86f0806bcc12805b41e7e4428"}, "domainSeparator": "0x80b61935a76550a2823abe775aa830ab5d33dabe8e884e2e7b5fc8fcd1085ee5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x06af07097c9eeb7fd685c692751d5c66db49c215": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x06af07097c9eeb7fd685c692751d5c66db49c215"}, "domainSeparator": "0x0b50407de9fa158c2cba01a99633329490dfd22989a150c20e8c7b4c1fb0fcc3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbba39fd2935d5769116ce38d46a71bde9cf03099": {"kind": "permit", "domain": {"name": "choise.com Token", "version": "1", "chainId": 1, "verifyingContract": "0xbba39fd2935d5769116ce38d46a71bde9cf03099"}, "domainSeparator": "0x5e126d32b790d4c70bead37029f9e5f93d3de39f429a6a56ae5da8fc4f7ce588", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0a6e18fb2842855c3af925310b0f50a4bfa17909": {"kind": "permit", "domain": {"name": "CoinPoker Chips", "version": "1", "chainId": 1, "verifyingContract": "0x0a6e18fb2842855c3af925310b0f50a4bfa17909"}, "domainSeparator": "0xa4f63d662a3ad0456b4b1b6dd844bb012e8f97dd2001c72cbac0c960b981d8ba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4d2192e0db26d54adf2e367f0d79252bc9f76591": {"kind": "permit", "domain": {"name": "Citizens", "version": "1", "chainId": 1, "verifyingContract": "0x4d2192e0db26d54adf2e367f0d79252bc9f76591"}, "domainSeparator": "0x134bb0a1be38bc276c75d561cd6fc9e04c208527101da669afb41c3324dc2397", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc6db556fd9ec09bab6dfea320e52d8476f61d424": {"kind": "permit", "domain": {"name": "<PERSON> <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xc6db556fd9ec09bab6dfea320e52d8476f61d424"}, "domainSeparator": "0xe3d46a9f661d81d42d5ed068b63defacd98aefd1e93dc9a6b0163f11cdb919b3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x68cfb82eacb9f198d508b514d898a403c449533e": {"kind": "permit", "domain": {"name": "Credmark", "version": "1", "chainId": 1, "verifyingContract": "0x68cfb82eacb9f198d508b514d898a403c449533e"}, "domainSeparator": "0x36ee5edd3c155f3ab1e09247358c30333c04ad93ae5cc1d720a95e6c3fbf850d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbc138bd20c98186cc0342c8e380953af0cb48ba8": {"kind": "permit", "domain": {"name": "Candle", "version": "1", "chainId": 1, "verifyingContract": "0xbc138bd20c98186cc0342c8e380953af0cb48ba8"}, "domainSeparator": "0xb2dc5adcc033154876a82fd3fbff3fe0f40db1f726956a4aa6794832edc3bacf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5c1d9aa868a30795f92fae903edc9eff269044bf": {"kind": "permit", "domain": {"name": "Changer", "version": "1", "chainId": 1, "verifyingContract": "0x5c1d9aa868a30795f92fae903edc9eff269044bf"}, "domainSeparator": "0xf5837f2fc7ccdd580f1c65f9c6f72133686fd59d35e1431a5a4e2acb942a75e1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x429876c4a6f89fb470e92456b8313879df98b63c": {"kind": "executeMetaTransaction::approve", "domain": {"name": "Cryption Network Token", "version": "1", "verifyingContract": "0x429876c4a6f89fb470e92456b8313879df98b63c", "salt": "0x0000000000000000000000000000000000000000000000000000000000000001"}, "domainSeparator": "0x8db54053a44305d53bf765b4d12897ba2968458118d552d2b0ed6d1994bb65c3", "domainSeparatorFn": "getDomainSeperator()", "nonce": "getNonce()"}, "0x8c27fd42fe55d9e9b802842ba3801e12d0770731": {"kind": "permit", "domain": {"name": "China Numero Uno", "version": "1", "chainId": 1, "verifyingContract": "0x8c27fd42fe55d9e9b802842ba3801e12d0770731"}, "domainSeparator": "0xe27efee5bae85185407f0c8778689a7dd9a2794dd51e345c55d2d59298d1c540", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x000000007a58f5f58e697e51ab0357bc9e260a04": {"kind": "permit", "domain": {"name": "Concave", "version": "1", "chainId": 1, "verifyingContract": "0x000000007a58f5f58e697e51ab0357bc9e260a04"}, "domainSeparator": "0xb3871005bf5dd12374191ec6483d5de5ac1162465efc8c4fc529a83dc2739bbd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc4bb7277a74678f053259cb1f96140347efbfd46": {"kind": "permit", "domain": {"name": "Coin of the champions", "version": "1", "chainId": 1, "verifyingContract": "0xc4bb7277a74678f053259cb1f96140347efbfd46"}, "domainSeparator": "0x496dc8ef6c1f7001a0f9a21965b4b6b904ed681ade36b31a8c7cd27e741ce1ba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf55be65ffb0f2885075bab53261ab6ad41e0f2c8": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xf55be65ffb0f2885075bab53261ab6ad41e0f2c8"}, "domainSeparator": "0xfe70caff0e260e662f87f570c8e8ec864145bcbddfe526357c713a3e8addea99", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb24cd494fae4c180a89975f1328eab2a7d5d8f11": {"kind": "permit", "domain": {"name": "Developer DAO", "version": "1", "chainId": 1, "verifyingContract": "0xb24cd494fae4c180a89975f1328eab2a7d5d8f11"}, "domainSeparator": "0x87f6bac5e93e4d22532020b1663a4053ea3e124be50df9a34911c0f3c2a40c91", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd417144312dbf50465b1c641d016962017ef6240": {"kind": "permit", "domain": {"name": "Covalent Query Token", "version": "1", "chainId": 1, "verifyingContract": "0xd417144312dbf50465b1c641d016962017ef6240"}, "domainSeparator": "0x89f5c4087abd5cc09525f23a0309f98e2e25202d4ad8de57550b1a7a3617e187", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6c43751fef27c956f7e75d5c345a65df1465f7e0": {"kind": "permit", "domain": {"name": "CR7 Token", "version": "1", "chainId": 1, "verifyingContract": "0x6c43751fef27c956f7e75d5c345a65df1465f7e0"}, "domainSeparator": "0x488b39817d64dafdeee2faf084c0a9a366836da27676802c814cfad8f1dcfda0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8e9c3b0e535d261f5e9a1eda43ee4836ca20795a": {"kind": "permit", "domain": {"name": "CRCL Token", "version": "1", "chainId": 1, "verifyingContract": "0x8e9c3b0e535d261f5e9a1eda43ee4836ca20795a"}, "domainSeparator": "0xa911f7312a6362fff5b7a2ae02f500160057df791f67261c253b2556e2d085b4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xaa61d5dec73971cd4a026ef2820bb87b4a4ed8d6": {"kind": "permit", "domain": {"name": "CRE8R DAO ", "version": "1", "chainId": 1, "verifyingContract": "0xaa61d5dec73971cd4a026ef2820bb87b4a4ed8d6"}, "domainSeparator": "0x7592dfa17d3f9e3f28a2a2c3d4c0fb859d9fddf6144cf5bed08be76aed2dd796", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x54efda7fae8316c210cbd1bd85f8cb87bdbda461": {"kind": "permit", "domain": {"name": "Crop Finance", "version": "1", "chainId": 1, "verifyingContract": "0x54efda7fae8316c210cbd1bd85f8cb87bdbda461"}, "domainSeparator": "0x2de1e6b872e86a39f06d5eeae3e629798a251b3126fc79f6d9cc06c711edaf01", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfdcdfa378818ac358739621ddfa8582e6ac1adcb": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Curio Stable Coin", "version": "1", "chainId": 1, "verifyingContract": "0xfdcdfa378818ac358739621ddfa8582e6ac1adcb"}, "domainSeparator": "0xf3fd51bf7278753d9bf4d766da4725ab5ea1152ac01995249edd461879c709ba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcb45625572253036e3e580702c9ce0343262b381": {"kind": "permit", "domain": {"name": "CthulhuCoin", "version": "1", "chainId": 1, "verifyingContract": "0xcb45625572253036e3e580702c9ce0343262b381"}, "domainSeparator": "0x3195f03b0fadb87c880a3823a786332c7da5089bf121080a13c0f6ea2ab88bf5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Cosmo Universal Power", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xd1fb8d95fe379c8ae5b585d72d63b8b460a0aa1f0cc6d5b118bb3633fef4e67c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Curve BTC Plus", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xbc399ea62ac0da5aded91c256076962e96390f8cc01efbeca15462a47460a60d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "cVToken", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xd82b13226a7b2165c4a982324a629da9dc5f2f7300253911c18035937ee370f0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Dai Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xdbb8cf42e1ecb028be3f3dbc922e1d878b963f411dc388ced501601c60f7c6f7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x40955d77f87123b71b145098358a60573ac7be96": {"kind": "permit", "domain": {"name": "DAISY", "version": "1", "chainId": 1, "verifyingContract": "0x40955d77f87123b71b145098358a60573ac7be96"}, "domainSeparator": "0xc3fda28274f938f245ee219f217cdd7bde09a24fb34022f4ae657d82af12deea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x01f3af11f2d93fb23ae33d336459957f3b7730ae": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x01f3af11f2d93fb23ae33d336459957f3b7730ae"}, "domainSeparator": "0x87a9229133b8805c138091c1f3d7de58f624dee9a3d42e51dfa7cda347d363a0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x419a6e348467be9958204c08a14b2ce52d637ffd": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x419a6e348467be9958204c08a14b2ce52d637ffd"}, "domainSeparator": "0x5f5d99cf06c7dfedb738df25e734d5e74b298bbb2ac13611f32f32245089a34f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x939b462ee3311f8926c047d2b576c389092b1649": {"kind": "permit", "domain": {"name": "DAPP TOKEN", "version": "1", "chainId": 1, "verifyingContract": "0x939b462ee3311f8926c047d2b576c389092b1649"}, "domainSeparator": "0xd8a8c9c4e4c6bc63ddd7e362a46951dc8f8deff1013b27a9ce7d936cce47fbba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xec5709c0750043cfb4b869dbc871e86efe18b4c5": {"kind": "permit", "domain": {"name": "Dark Matter", "version": "1", "chainId": 1, "verifyingContract": "0xec5709c0750043cfb4b869dbc871e86efe18b4c5"}, "domainSeparator": "0x10a51b1ef7d2bcf16006e309e0258648b920b3cca8eab323b2879fbca4835a05", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6330e5e49022d0b424a612bfb539f99a24f1c41d": {"kind": "permit", "domain": {"name": "Dawgsta", "version": "1", "chainId": 1, "verifyingContract": "0x6330e5e49022d0b424a612bfb539f99a24f1c41d"}, "domainSeparator": "0x0cfe13789ca827aea9ec5468038787656f90dc6606cb19bf749069126d9435c2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x64a77277e37d44957fe5815d6ff442ab8b16cc29": {"kind": "permit", "domain": {"name": "SpaceDawgs", "version": "1", "chainId": 1, "verifyingContract": "0x64a77277e37d44957fe5815d6ff442ab8b16cc29"}, "domainSeparator": "0xd5da49c493db326fffa20ab9103f626c5727f084b874335aeafa2fae410e9a84", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xad038eb671c44b853887a7e32528fab35dc5d710": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>rrowing Right", "version": "1", "chainId": 1, "verifyingContract": "0xad038eb671c44b853887a7e32528fab35dc5d710"}, "domainSeparator": "0xd70c5a91c034ae556adea4a4208575466a13f1986ad8d938d1032fd48eb07e76", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4ece5c5cfb9b960a49aae739e15cdb6cfdcc5782": {"kind": "permit", "domain": {"name": "Doont Buy", "version": "1", "chainId": 1, "verifyingContract": "0x4ece5c5cfb9b960a49aae739e15cdb6cfdcc5782"}, "domainSeparator": "0xea3fb88cfe04ad50d932a64a8e5f581c3b0a2f426027ea4a8fcf15aff27c70f5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x045da4bfe02b320f4403674b3b7d121737727a36": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x045da4bfe02b320f4403674b3b7d121737727a36"}, "domainSeparator": "0x9d970ad54a1cd6b9b9e8e383d29320b6f24b0216a664db18c49a69e8beb3d206", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3c64beb87c58ebc7f666727b741fefb27e9a7d56": {"kind": "permit", "domain": {"name": "Degen Defender", "version": "1", "chainId": 1, "verifyingContract": "0x3c64beb87c58ebc7f666727b741fefb27e9a7d56"}, "domainSeparator": "0x7bcc1e7aa4814bd84b2f8a3e29fe1508586db594cca1a2e5f03cb2feac9433cb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7caad772532339af66d886520b8c546f6758ee4d": {"kind": "permit", "domain": {"name": "Decentralized Finance Token", "version": "1", "chainId": 1, "verifyingContract": "0x7caad772532339af66d886520b8c546f6758ee4d"}, "domainSeparator": "0xd2bbd0ec9572648bb812b89cda4d12e5336e963b7a183004e9b996e0878b9bd9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd6efcd22f0e3a4a9c35a8aabff52350dfb0c2621": {"kind": "permit", "domain": {"name": "DegenLabsToken", "version": "1", "chainId": 1, "verifyingContract": "0xd6efcd22f0e3a4a9c35a8aabff52350dfb0c2621"}, "domainSeparator": "0x3b2f96737b462049f4fbe2671a28b346596506e8d9bfe1de52ec7b3b8ee28e05", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7b19929f6fe4e468ed7568122f1000be8cf226cf": {"kind": "permit", "domain": {"name": "Delphi", "version": "1", "chainId": 1, "verifyingContract": "0x7b19929f6fe4e468ed7568122f1000be8cf226cf"}, "domainSeparator": "0xf2460654acfdf73e4e65309df4173cf91c053ead223656a6eca0a1a282baf4a1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7502931720447014d198e3405bb1c067ee641102": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "defigold.network", "version": "1", "chainId": 1, "verifyingContract": "0x7502931720447014d198e3405bb1c067ee641102"}, "domainSeparator": "0x017cd21e5954201385110270db006bfd91eba54ab2e76d1a85afceb947771ffe", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9284101d05e1a2a7efbae2c8904aa023bdae5a6e": {"kind": "permit", "domain": {"name": "Degen Gamers Club", "version": "1", "chainId": 1, "verifyingContract": "0x9284101d05e1a2a7efbae2c8904aa023bdae5a6e"}, "domainSeparator": "0xa405c69a3d297e53a4b8d62756bd5e1eaba320659494266651079543ca36f340", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7b8fbdf847c343a88e24524f00bda64842469d9d": {"kind": "permit", "domain": {"name": "DIAMOND", "version": "1", "chainId": 1, "verifyingContract": "0x7b8fbdf847c343a88e24524f00bda64842469d9d"}, "domainSeparator": "0x2644b90d7818da2b2c053f6eaa2d7bb6acda1fe72340ecd5c27370a2b3ba7169", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6df3faaf5ba546831c03432d79e356fb957ef012": {"kind": "permit", "domain": {"name": "DeriswaPv1", "version": "1", "chainId": 1, "verifyingContract": "0x6df3faaf5ba546831c03432d79e356fb957ef012"}, "domainSeparator": "0x4f9a5de8bc9a14736668c72dbcddfbfa2914caf470b72cd0ab75f2bdc7f71b11", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x07adc37c53cc791c502fff9314660999eedd3c20": {"kind": "permit", "domain": {"name": "Toolbox", "version": "1", "chainId": 1, "verifyingContract": "0x07adc37c53cc791c502fff9314660999eedd3c20"}, "domainSeparator": "0x65db05744d8271390a7331b8342bb5f50126187d59d12a234daacdccd62282f8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x144e3b02e08e644ffbb7ff652763f5b72ee22701": {"kind": "permit", "domain": {"name": "DiamondDAO", "version": "1", "chainId": 1, "verifyingContract": "0x144e3b02e08e644ffbb7ff652763f5b72ee22701"}, "domainSeparator": "0x8e0ce7e2ec351bb437185caf304adf1108bec497d38f29a90b8c9642763d3129", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xef6344de1fcfc5f48c30234c16c1389e8cdc572c": {"kind": "permit", "domain": {"name": "DNA", "version": "1", "chainId": 1, "verifyingContract": "0xef6344de1fcfc5f48c30234c16c1389e8cdc572c"}, "domainSeparator": "0x09526b61bd3ff1ae4d7271e0ec7b520ac1db69b488b803961abf4733d99664e9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf8e9f10c22840b613cda05a0c5fdb59a4d6cd7ef": {"kind": "permit", "domain": {"name": "Dogs Of Elon", "version": "1", "chainId": 1, "verifyingContract": "0xf8e9f10c22840b613cda05a0c5fdb59a4d6cd7ef"}, "domainSeparator": "0xbd8159c0b7d41cc4d47d1c82c271f98711807dba6b60d86280425d63556872b8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4206931337dc273a630d328da6441786bfad668f": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x4206931337dc273a630d328da6441786bfad668f"}, "domainSeparator": "0xbe2230ba266e24f77b0ddb0484b1240c345cab917242c0986c6bc1637a49f9a8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xca9bef33744581b33f5d8e7dc715db6c4db37c24": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xca9bef33744581b33f5d8e7dc715db6c4db37c24"}, "domainSeparator": "0x9c2c1c0b624e70495dcfb9aa71915c2149bbe68efcfcd86b3ebce2288a33c7c5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf7cff7098a431a905970962eb86044ce6e7a0412": {"kind": "permit", "domain": {"name": "DogeV<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xf7cff7098a431a905970962eb86044ce6e7a0412"}, "domainSeparator": "0xedafa6c5888694a029b53bee2a95b5b68d0b2fbb8c747d72c832b047992e697a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0361b0864ab8c744c4d959730956715c3dc95589": {"kind": "permit", "domain": {"name": "DOGGZ", "version": "1", "chainId": 1, "verifyingContract": "0x0361b0864ab8c744c4d959730956715c3dc95589"}, "domainSeparator": "0x03fec09475ffcd4ce95370b6ff15fe76cee534c020e7a3c8ab48227356d04c00", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5b3240b6be3e7487d61cd1afdfc7fe4fa1d81e64": {"kind": "permit", "domain": {"name": "Balancer DOLA bb-a-usd Stable", "version": "1", "chainId": 1, "verifyingContract": "0x5b3240b6be3e7487d61cd1afdfc7fe4fa1d81e64"}, "domainSeparator": "0xf76b57173b68f5768bde2b8da8f150b443e3f05847e65df13d00287988395fc1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x058bc8ef040bd3971418e36aa88b64899378ccf4": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x058bc8ef040bd3971418e36aa88b64899378ccf4"}, "domainSeparator": "0x29bd219773af1d13721414ba38f09c630924041683352e061608423fe6dc088c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb31ef9e52d94d4120eb44fe1ddfde5b4654a6515": {"kind": "permit", "domain": {"name": "DOSE", "version": "1", "chainId": 1, "verifyingContract": "0xb31ef9e52d94d4120eb44fe1ddfde5b4654a6515"}, "domainSeparator": "0x3bc6379b4ec5664202a945e06bf4983438bcaf0d223bc8b9d1d79ed7aa06f601", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfb62ae373aca027177d1c18ee0862817f9080d08": {"kind": "permit", "domain": {"name": "My DeFi Pet <PERSON>", "chainId": 1, "verifyingContract": "0xfb62ae373aca027177d1c18ee0862817f9080d08"}, "domainSeparator": "0xb987ff1648b171ac4ff4692ec93c6cf86f8a1f73614a7033bfb9e9b645156d50", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4b815591f596f2a07c0ffddce41f6f2da6304c53": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x4b815591f596f2a07c0ffddce41f6f2da6304c53"}, "domainSeparator": "0x10b1bc9d7c2093eff94c5250f4a17eae8e6a2f582c15c974c97e7bd6a5ee8e6f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x49428f057dd9d20a8e4c6873e98afd8cd7146e3b": {"kind": "permit", "domain": {"name": "Signata DAO", "version": "1", "chainId": 1, "verifyingContract": "0x49428f057dd9d20a8e4c6873e98afd8cd7146e3b"}, "domainSeparator": "0xcc39bd85ef68339ee8924ddaf6cf61ca9bebbea77b4a67a77a7378920bf77f35", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x605d26fbd5be761089281d5cec2ce86eea667109": {"kind": "permit", "domain": {"name": "Digital Standard Unit", "version": "1", "chainId": 1, "verifyingContract": "0x605d26fbd5be761089281d5cec2ce86eea667109"}, "domainSeparator": "0x1ca425eb3dd2239393518c875a9fa0e87e43e9f075888ef3c1b590383b73b6ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc90b6004e077b175e4e87a2a0615691be6c26095": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xc90b6004e077b175e4e87a2a0615691be6c26095"}, "domainSeparator": "0xd28ae19a1c9f14e0321630de8c14086e4f7b35afd2fa17cbb21fe3187405975e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7dee45dff03ec7137979586ca20a2f4917bac9fa": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x7dee45dff03ec7137979586ca20a2f4917bac9fa"}, "domainSeparator": "0xda2c05c7035c47da82b28363140c133c7c11bb0ccd51febd569253b3c69a6a4b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa1d65e8fb6e87b60feccbc582f7f97804b725521": {"kind": "permit", "domain": {"name": "DXdao", "version": "2", "chainId": 1, "verifyingContract": "0xa1d65e8fb6e87b60feccbc582f7f97804b725521"}, "domainSeparator": "0xc5df7b4fee299920f2a094d4a526ee7eefd93942890b65c3e5703455ed58ba8a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x88aa4a6c5050b9a1b2aa7e34d0582025ca6ab745": {"kind": "permit", "domain": {"name": "Dexpools Token", "version": "1", "chainId": 1, "verifyingContract": "0x88aa4a6c5050b9a1b2aa7e34d0582025ca6ab745"}, "domainSeparator": "0x383d9742b233b6aec12df58269839deca32de8c1848867ab704a0cf2f61cb81d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x92d6c1e31e14520e676a687f0a93788b716beff5": {"kind": "permit", "domain": {"name": "dYdX", "version": "1", "chainId": 1, "verifyingContract": "0x92d6c1e31e14520e676a687f0a93788b716beff5"}, "domainSeparator": "0x17e76dd75f3dfe3e50d31322433cc1405e898404a5724da2d4804cab81e0a88a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x24244f8060eace82b36eb46c5d313bd0a72e3bcb": {"kind": "permit", "domain": {"name": "dYdX", "version": "1", "chainId": 1, "verifyingContract": "0x24244f8060eace82b36eb46c5d313bd0a72e3bcb"}, "domainSeparator": "0x4a08fafb2e5e33012c450b5038ae6a3d180ecd9b0bdfa496ac1f80bb420b2439", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x529119733d33f4779b7a0bbc2cd43746bcfc3929": {"kind": "permit", "domain": {"name": "DyneCoin", "version": "1", "chainId": 1, "verifyingContract": "0x529119733d33f4779b7a0bbc2cd43746bcfc3929"}, "domainSeparator": "0xde0b0c0522b74cff18b396eb5642e055db69950d9548ba7da5890a8a7d08b05f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2ce99ddbb794101eb81539ade1c9792e8110749a": {"kind": "permit", "domain": {"name": "Effective Altruism", "version": "1", "chainId": 1, "verifyingContract": "0x2ce99ddbb794101eb81539ade1c9792e8110749a"}, "domainSeparator": "0x8947cccc6fb7c397bb8a86c28688b5fc28ba365d15ed38904433f4bcdc764cd9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xea5edef1287afdf9eb8a46f9773abfc10820c61c": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xea5edef1287afdf9eb8a46f9773abfc10820c61c"}, "domainSeparator": "0xd1506caf8f1567c88ef88798fc3ed28597683b0dc3b277bd697d9c55ed659708", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa01b656e49efbb8210d882a1f1a4d10f5cada8cc": {"kind": "permit", "domain": {"name": "Eco Gold", "version": "1", "chainId": 1, "verifyingContract": "0xa01b656e49efbb8210d882a1f1a4d10f5cada8cc"}, "domainSeparator": "0xcbb70ec3c2f67987bb68d752c248ec627546e53baf37e9e4e3076dfbebeb8445", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Ecto", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0ecfc8e91a68b57a194cd07353085091db9a4b1168b62999fb30264877e56c72", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "EarnETH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x05119444d5ab144d2ba05d366c3f196a01938c28d686b9ad51cb977128c28e1f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Ethereum Wrapped Filecoin", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xbcf05f5cd3ea4143e45d7c8639d2a72fe1f3698d78cd6fdfa5376acf35efead8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "ELA on Ethereum", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xda5ddbed30ece1d728ba2d7e2c7d005758e366556d7f03e3f3b2b8d3040b7b68", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Elk", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x9f8ab0b4bf551e7e5250ad6b68be4b13ddac93c1c881075af6922fa38d542a49", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Ethereum Mix", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x3f17678c333080918e9a31bb0650b2da43ea1f0cd3857f1ecf38cf7c17fa413a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "EON", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x57e7b2bdab895e82e5efc4b5a582053e01e772c35368b2a86176f83e541e2a95", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "ETHPETS", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x6d79cbb0bc424dfaefe9a4550ad4e7b17e02eedb21b730124cc1a33a62b31c0d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Element Principal Token yvDAI-16OCT21", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xa085fd9184fccffd4f6eb3e46e9bb82239f0c7017219b989f13d42488429e8d2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Element Principal Token yvDAI-28JAN22", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xb01d8cb5049f11680670049513cf1d9230b2291860d5dfb235055d89e493ef13", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x76a34d72b9cf97d972fb0e390eb053a37f211c74": {"kind": "permit", "domain": {"name": "Element Principal Token yvUSDC-17DEC21", "version": "1", "chainId": 1, "verifyingContract": "0x76a34d72b9cf97d972fb0e390eb053a37f211c74"}, "domainSeparator": "0xebe683a14a331b952384a35b99fd7ab8da1866b310d2ea2895ab5551e3c19325", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Element Principal Token yvUSDC-28JAN22", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x119304c6d7341d686ff8be55327b3a9312ce9fdb884bc14d4525312d0bba6f0d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Ethix", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xb797908f22fa2c728256dc2ed507360eaf227941684413c639800e11f709a110", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "ETradingStakeToken", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xf2452c1f823df33c2e7485e436f2951fbbc1128bd59ae000f237cae4b0acc9db", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "<PERSON>uler", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x033df809c37647aaca6909da290b9c07248dee4ccc343f47191e584a60295e75", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdbd9f3d57f199b44ea70f624f5cd14ddc1ad2ac7": {"kind": "permit", "domain": {"name": "Excalibur", "version": "1", "chainId": 1, "verifyingContract": "0xdbd9f3d57f199b44ea70f624f5cd14ddc1ad2ac7"}, "domainSeparator": "0x78fe1268b49097127bee3c9607e92495357a917a2c16624f6dab38765d678e3e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe728834bda6572a39c3092f8dda82029bb60da80": {"kind": "permit", "domain": {"name": "Exfinet", "version": "1", "chainId": 1, "verifyingContract": "0xe728834bda6572a39c3092f8dda82029bb60da80"}, "domainSeparator": "0x80043425c50585a5f0dddef88e8fab1fe77484ebd61e3d253d6c9a665512406b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8c6fa66c21ae3fc435790e451946a9ea82e6e523": {"kind": "permit", "domain": {"name": "MetaFabric", "version": "1", "chainId": 1, "verifyingContract": "0x8c6fa66c21ae3fc435790e451946a9ea82e6e523"}, "domainSeparator": "0xb3121e098a379df478c5a7170a0fc69a6527e4fd135ce9e0ca7df48bbb0c73f2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x59b4bcbdab80643575fe0a971b5e11247946109a": {"kind": "permit", "domain": {"name": "The Factor", "version": "1", "chainId": 1, "verifyingContract": "0x59b4bcbdab80643575fe0a971b5e11247946109a"}, "domainSeparator": "0xa72925b5e32a95120999b96802c80ee1ff9d8e0d80c25204c740e272fd02813d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7de2b6745c3a4ab23de9b459f0639e049c275fd0": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Fairmint Dev Init", "version": "2", "chainId": 1, "verifyingContract": "0x7de2b6745c3a4ab23de9b459f0639e049c275fd0"}, "domainSeparator": "0xe73dad4f1fee09bf1944cba0f097e68e2c5faa8b64a60a37339896e76efeb53b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0382f5ca67a15a4fd91959904e9681a8319a4c7a": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Fairmint Stage Init", "version": "2", "chainId": 1, "verifyingContract": "0x0382f5ca67a15a4fd91959904e9681a8319a4c7a"}, "domainSeparator": "0xe2fa3bad8453cfc024d737565182ff2d2079cb7bd1ad8588abea1a065e707ffb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9ec0862ce715ea4fd830074be6bee4558db8a071": {"kind": "permit", "domain": {"name": "FeeToken", "version": "1", "chainId": 1, "verifyingContract": "0x9ec0862ce715ea4fd830074be6bee4558db8a071"}, "domainSeparator": "0x4385a3fdf1e0508fcb1c00c333b9ae91364a665d5b6a077f8a4af042a350297f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x985572429deb3acc47e019af6b3da4620175e553": {"kind": "permit", "domain": {"name": "FeeToken", "version": "1", "chainId": 1, "verifyingContract": "0x985572429deb3acc47e019af6b3da4620175e553"}, "domainSeparator": "0x07436b4ebdac5a5d31e348052c0c07a8c1104a0e74278c132f686955968bdb8a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf344b01da08b142d2466dae9e47e333f22e64588": {"kind": "permit", "domain": {"name": "FemboyDAO", "version": "1", "chainId": 1, "verifyingContract": "0xf344b01da08b142d2466dae9e47e333f22e64588"}, "domainSeparator": "0x03db843de9b48e8528f83b1f26add7805b408859186e862519f6457e33939972", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9fb83c0635de2e815fd1c21b3a292277540c2e8d": {"kind": "permit", "domain": {"name": "FevrToken", "version": "1", "chainId": 1, "verifyingContract": "0x9fb83c0635de2e815fd1c21b3a292277540c2e8d"}, "domainSeparator": "0x923b729f15222155eb7bcd695abc2efb31ef7d1083fa9ed30d09e3ce023eb192", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x586aa273f262909eef8fa02d90ab65f5015e0516": {"kind": "permit", "domain": {"name": "Fixed Income Asset Token", "version": "1", "chainId": 1, "verifyingContract": "0x586aa273f262909eef8fa02d90ab65f5015e0516"}, "domainSeparator": "0x94786d3bb7f831b91877ce463094469cc989126b058b7b9b795f31b4bfc8c78c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb1f1f47061a7be15c69f378cb3f69423bd58f2f8": {"kind": "permit", "domain": {"name": "Flashstake", "version": "1", "chainId": 1, "verifyingContract": "0xb1f1f47061a7be15c69f378cb3f69423bd58f2f8"}, "domainSeparator": "0x18e67701e3ea65b05a598bc03296c126c51e2dc5cf4a2fb2e610728ae1ee256d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb05097849bca421a3f51b249ba6cca4af4b97cb9": {"kind": "permit", "domain": {"name": "Float Protocol: FLOAT", "version": "1", "chainId": 1, "verifyingContract": "0xb05097849bca421a3f51b249ba6cca4af4b97cb9"}, "domainSeparator": "0x6edcb21bc46a1f48cdc3b1f0450cdcbc70347219c6e48b05fd36f8cb0f53bd35", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf59257e961883636290411c11ec5ae622d19455e": {"kind": "permit", "domain": {"name": "Floor", "version": "1", "chainId": 1, "verifyingContract": "0xf59257e961883636290411c11ec5ae622d19455e"}, "domainSeparator": "0xfc8c1f2531fcad05713e630a5081995cd25c9989374dd12fc4847ebc19d47a47", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5a1912749d2b7c3cdb832b81601c8c3f437a0de8": {"kind": "permit", "domain": {"name": "FLYZ", "version": "1", "chainId": 1, "verifyingContract": "0x5a1912749d2b7c3cdb832b81601c8c3f437a0de8"}, "domainSeparator": "0xe3d7307cd9c246375df288e73d30a86920eb392b873f9b105100f5c8dc0ced66", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x29ceddcf0da3c1d8068a7dfbd0fb06c2e438ff70": {"kind": "permit", "domain": {"name": "FreelaToken", "version": "1", "chainId": 1, "verifyingContract": "0x29ceddcf0da3c1d8068a7dfbd0fb06c2e438ff70"}, "domainSeparator": "0xcea49a33597a6466c7b411b947c2a9efddd9622beac358914ef61855e03959cf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcf0bcf85082e3cfe591bd2451bc4b46faa34c7de": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xcf0bcf85082e3cfe591bd2451bc4b46faa34c7de"}, "domainSeparator": "0x1179d39820817ccd1dfbfe43c403dfe1978afeccdbf53a898bb461a4f7578092", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5e8422345238f34275888049021821e8e08caa1f": {"kind": "permit", "domain": {"name": "Fr<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x5e8422345238f34275888049021821e8e08caa1f"}, "domainSeparator": "0xf76bc0789b1168d8167e9ab7fe582017fc10c446b759047e0b1cb2d4451d7ce6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa24390c62186a8d265344e914f0fd962b81b5f13": {"kind": "permit", "domain": {"name": "<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xa24390c62186a8d265344e914f0fd962b81b5f13"}, "domainSeparator": "0x88b9f6414ab4603c98f91b898f4cd398d7111bb6b5cb05418e5337c9328189ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x661b6ccd0edeed2b351f6ec5452455b064518592": {"kind": "permit", "domain": {"name": "Flight to Earn", "version": "1", "chainId": 1, "verifyingContract": "0x661b6ccd0edeed2b351f6ec5452455b064518592"}, "domainSeparator": "0x08b3e460513fb9b9a9a5fb095f10ce440aa429cebadbd8d1661598d870408cba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3bedae4dba3c3561b61381441098e25147fc363a": {"kind": "permit", "domain": {"name": "FUCK ALL RUGS", "version": "1", "chainId": 1, "verifyingContract": "0x3bedae4dba3c3561b61381441098e25147fc363a"}, "domainSeparator": "0xee5c54b04c8a006aa9d43b805af6d8ae42b29d847a6acd0ab40a642a47b6dc9f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb4dffa52fee44bd493f12d85829d775ec8017691": {"kind": "permit", "domain": {"name": "Congruent DAO Token", "version": "1", "chainId": 1, "verifyingContract": "0xb4dffa52fee44bd493f12d85829d775ec8017691"}, "domainSeparator": "0x64535ea7450749e949bb36b5090e9ee8778f1e8c184e53b3f3994ceb23a558b2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9fda7ceec4c18008096c2fe2b85f05dc300f94d0": {"kind": "permit", "domain": {"name": "PolyGaj <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x9fda7ceec4c18008096c2fe2b85f05dc300f94d0"}, "domainSeparator": "0xfc815fa8c7781ffb8ee67b21b28340498a78a1de44a9cd19c8fc410dbe14fa98", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5faa989af96af85384b8a938c2ede4a7378d9875": {"kind": "permit", "domain": {"name": "Project Galaxy", "version": "1", "chainId": 1, "verifyingContract": "0x5faa989af96af85384b8a938c2ede4a7378d9875"}, "domainSeparator": "0x67c157a481eaccbcae83f0864e7d4db60cae548a16d0b63f88eff13be8ae2660", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2275d4937b6bfd3c75823744d3efbf6c3a8de473": {"kind": "permit", "domain": {"name": "Go<PERSON>r", "version": "1", "chainId": 1, "verifyingContract": "0x2275d4937b6bfd3c75823744d3efbf6c3a8de473"}, "domainSeparator": "0xe7426cc538502e4c324d4327fc221585b9be7f643448648e75c94d2bd3cf3f66", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x431b366be6069ae62f8121a901c6160b34af7f80": {"kind": "permit", "domain": {"name": "GoldDAX", "version": "1", "chainId": 1, "verifyingContract": "0x431b366be6069ae62f8121a901c6160b34af7f80"}, "domainSeparator": "0xc514c80801ba4a627896d2be672d5054acabecae5e927ba15d5d75d2f1c86423", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9a9821cb2d96aa9768a48688de76429bf7ba728a": {"kind": "permit", "domain": {"name": "Gems", "version": "1", "chainId": 1, "verifyingContract": "0x9a9821cb2d96aa9768a48688de76429bf7ba728a"}, "domainSeparator": "0x101abcb353f386ae92939070b5454db4ce212e85b4ca5129969084012266833d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9b17baadf0f21f03e35249e0e59723f34994f806": {"kind": "permit", "domain": {"name": "NFTmall GEM Token", "version": "1", "chainId": 1, "verifyingContract": "0x9b17baadf0f21f03e35249e0e59723f34994f806"}, "domainSeparator": "0x54c8cddfd115f6865d5d0d11bc7c2dbfde02513ae6f28145d971b532a099e282", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x21413c119b0c11c5d96ae1bd328917bc5c8ed67e": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x21413c119b0c11c5d96ae1bd328917bc5c8ed67e"}, "domainSeparator": "0x84e272a906616805ff6568263cc662b080427fca9cc77e69848232cd28eaed11", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x444444444444c1a66f394025ac839a535246fcc8": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x444444444444c1a66f394025ac839a535246fcc8"}, "domainSeparator": "0xd951a38113188a785d6753c8c4b58034c16acb79f12f2984aaf2450160fe2feb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7dd9c5cba05e151c895fde1cf355c9a1d5da6429": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Golem Network Token", "version": "1", "chainId": 1, "verifyingContract": "0x7dd9c5cba05e151c895fde1cf355c9a1d5da6429"}, "domainSeparator": "0xfb7f74aaa2caa8fda0599a3aaf2380d46376dee141c58ef6cffbb1fd3a1761d5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x70c5f366db60a2a0c59c4c24754803ee47ed7284": {"kind": "permit", "domain": {"name": "Glove", "version": "1", "chainId": 1, "verifyingContract": "0x70c5f366db60a2a0c59c4c24754803ee47ed7284"}, "domainSeparator": "0xf1d42421e8ef69fc1b3df5a03f327e927e7a965e70525fc143638195e0286842", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe58eb0bb13a71d7b95c4c3cbe6cb3dbb08f9cbfb": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0xe58eb0bb13a71d7b95c4c3cbe6cb3dbb08f9cbfb"}, "domainSeparator": "0x9a1c800622060cd9067ba4e22d05bda37ed148788499db01b620aac7e4643948", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa9b584ec492cf6f641c9d37ea23bf5229139b419": {"kind": "permit", "domain": {"name": "Genesis", "version": "1", "chainId": 1, "verifyingContract": "0xa9b584ec492cf6f641c9d37ea23bf5229139b419"}, "domainSeparator": "0xb90008c4e4800f4891f2d945e52d57c7adfca49da38702a3f805881dc4f49f0e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4097cc783596b34a971f78dbc0da502bd8936d90": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x4097cc783596b34a971f78dbc0da502bd8936d90"}, "domainSeparator": "0xdffa7c227d2d773c019e4c4237cafd92805629cc35bacbd97f7308494a8ec63c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc2a3a4fd22c9054615f45ef5753eb0c5a9ebe5e8": {"kind": "permit", "domain": {"name": "Gobbler Union", "version": "1", "chainId": 1, "verifyingContract": "0xc2a3a4fd22c9054615f45ef5753eb0c5a9ebe5e8"}, "domainSeparator": "0x0800c8bb894eafbcd03088e8424918158fe52ccbcff13881454552e3aefe0433", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc57a0af6d8836d61ab2816d1b4d0c8918007132c": {"kind": "permit", "domain": {"name": "Goberian", "version": "1", "chainId": 1, "verifyingContract": "0xc57a0af6d8836d61ab2816d1b4d0c8918007132c"}, "domainSeparator": "0xe6bb20340ca37f15945a01491c12f7cff0505f73c579f5bba5a37a4fd0721257", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7be647634a942e73f8492d15ae492d867ce5245c": {"kind": "permit", "domain": {"name": "Feudalz Goldz", "version": "1", "chainId": 1, "verifyingContract": "0x7be647634a942e73f8492d15ae492d867ce5245c"}, "domainSeparator": "0x894a871ffe89a6e441b10f47ff6cdb07c5a711d58f40bd4ba0d214951b4c5ff8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdcabdbdf086e1b0398418cdd10f8fbbf827cbb0b": {"kind": "permit", "domain": {"name": "Goo", "version": "1", "chainId": 1, "verifyingContract": "0xdcabdbdf086e1b0398418cdd10f8fbbf827cbb0b"}, "domainSeparator": "0xc5b603ace64e3e5172ec8fc1bc4a38f4d579498e8b1e5d4a53082bc5dee4b7ce", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x600000000a36f3cd48407e35eb7c5c910dc1f7a8": {"kind": "permit", "domain": {"name": "Goo", "version": "1", "chainId": 1, "verifyingContract": "0x600000000a36f3cd48407e35eb7c5c910dc1f7a8"}, "domainSeparator": "0x9418a117e163a3caf4e9a5db65e01f6985ca4ad8497825c79a77ba431947a3e3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc9393609a47f5744ce98369208b9dc66224e6b5d": {"kind": "permit", "domain": {"name": "GoldOnSteroids", "version": "1", "chainId": 1, "verifyingContract": "0xc9393609a47f5744ce98369208b9dc66224e6b5d"}, "domainSeparator": "0x844bcb698e890ffaa69a86de31f560ba824e4d9dffe2bd4c49332a69052b5e07", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x005e217b7fa172558179f98635b7d40292fb5994": {"kind": "permit", "domain": {"name": "Giga Protocol", "version": "1", "chainId": 1, "verifyingContract": "0x005e217b7fa172558179f98635b7d40292fb5994"}, "domainSeparator": "0x2fe5a9f7291adeec3fa090a21c6bc3b1e2e223a20a15319f3659dd27ed6beda9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd8e2f184eedc79a9bde9eb7e34b0ff34e98692b7": {"kind": "permit", "domain": {"name": "Gravity", "version": "1", "chainId": 1, "verifyingContract": "0xd8e2f184eedc79a9bde9eb7e34b0ff34e98692b7"}, "domainSeparator": "0xa038d211e52b195fd56b043a0fd46d6644d44138995c63c5522365d1e68b1b8f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "GRILLA", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1daec8d3393b9a54b830aed5e8c5462cafcde431813f093fe64a301c67ad7e08", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Gwei Token (Forked from WETH10)", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x6732598fb284a4d7453caedbc678a49be915d366e53dd63bb322373a1e96c821", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "GWS", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x413c273bddc3f38913c6853e2ac99ad40f653978aa5994c4dad57706e595160c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "H2O", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0782df5f906f9837bebbecc396c1bba239f1eaffb25856cc2cae21dc73987c0b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0e29e5abbb5fd88e28b2d355774e73bd47de3bcd": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Hakka Finance", "version": "1", "chainId": 1, "verifyingContract": "0x0e29e5abbb5fd88e28b2d355774e73bd47de3bcd"}, "domainSeparator": "0x745af948ba14630b2d2455e8ad5d621021df45df8a0a19765d5a6ebe8bb9eef4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc3b20cf63b36909cd43a91c2786c373bc7d708ab": {"kind": "permit", "domain": {"name": "Halcyon <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xc3b20cf63b36909cd43a91c2786c373bc7d708ab"}, "domainSeparator": "0xe2c76018e166cbb5543730ce473007e26c244c93df2e5f59e79e5e9ea657e5c7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x72cfebbeec86009c3c6860484dca5636349f372d": {"kind": "permit", "domain": {"name": "Hammer Protocol", "version": "1", "chainId": 1, "verifyingContract": "0x72cfebbeec86009c3c6860484dca5636349f372d"}, "domainSeparator": "0x58b7eb5706d42bcf1cf7cde264f8da21be44ced0c15227d354bc112c27b045fb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0c90c57aaf95a3a87eadda6ec3974c99d786511f": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x0c90c57aaf95a3a87eadda6ec3974c99d786511f"}, "domainSeparator": "0x8ec453966133bcadf779198e1bcbdf6bd8f89ad5cfd85389b89e463614047920", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0ace32f6e87ac1457a5385f8eb0208f37263b415": {"kind": "permit", "domain": {"name": "Habitat Token", "chainId": 1, "verifyingContract": "0x0ace32f6e87ac1457a5385f8eb0208f37263b415"}, "domainSeparator": "0x779b979c1ce4c733450c0d419a833f5f66cc0467d751e1ce2c31502052ace8e0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdac657ffd44a3b9d8aba8749830bf14beb66ff2d": {"kind": "permit", "domain": {"name": "humanDAO", "version": "1", "chainId": 1, "verifyingContract": "0xdac657ffd44a3b9d8aba8749830bf14beb66ff2d"}, "domainSeparator": "0xd5a73545db3c4efb3d915ab2ae38eacd496757651bb6f6939dcc4281962ede92", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0f45a96fd1d9c6dceffefe60965e026d396ec25d": {"kind": "permit", "domain": {"name": "Sustedible DAO", "version": "1", "chainId": 1, "verifyingContract": "0x0f45a96fd1d9c6dceffefe60965e026d396ec25d"}, "domainSeparator": "0xd36ff2ea94fbaffbcb0df8a9dc825b1cfd300e3e2211547a1f9876bd5db6b2a2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd520ddbdf81323f4b9a0ea0adbb692ebef6ce280": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xd520ddbdf81323f4b9a0ea0adbb692ebef6ce280"}, "domainSeparator": "0x872eb2aea140eb95c9a6d40abbc90f94d2fe03d4ce07def626a131e352dc58d0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcfaaeb5e659d34d781c477eee27c5c07e1066247": {"kind": "permit", "domain": {"name": "HNA G", "version": "1", "chainId": 1, "verifyingContract": "0xcfaaeb5e659d34d781c477eee27c5c07e1066247"}, "domainSeparator": "0x57a5337adfcacf66bd0011e4350087cf216b9e2a4dd2c407be6c9396cbce2635", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x10010078a54396f62c96df8532dc2b4847d47ed3": {"kind": "permit", "domain": {"name": "Hundred Finance", "version": "1", "chainId": 1, "verifyingContract": "0x10010078a54396f62c96df8532dc2b4847d47ed3"}, "domainSeparator": "0x6d72741238d9c40aac32563843d2e321678f3adb08bda09e44704ff551af1244", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc3589f56b6869824804a5ea29f2c9886af1b0fce": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Honey", "version": "1", "chainId": 1, "verifyingContract": "0xc3589f56b6869824804a5ea29f2c9886af1b0fce"}, "domainSeparator": "0x1e7c211193da57b4cec51ac8aeee79a44ee466904747890ebe1449f3d2b56d51", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x33b4fe5e40e4903a0849ca97b3292eac3eb0aa36": {"kind": "permit", "domain": {"name": "Honey", "version": "1", "chainId": 1, "verifyingContract": "0x33b4fe5e40e4903a0849ca97b3292eac3eb0aa36"}, "domainSeparator": "0x1d7d865bd310105affd11467fcf8a6090b2f55948b28c848cce408b6c630e675", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd1ac902c7d058055cab36373159be35391722f29": {"kind": "permit", "domain": {"name": "Harumble Inu", "version": "1", "chainId": 1, "verifyingContract": "0xd1ac902c7d058055cab36373159be35391722f29"}, "domainSeparator": "0x8ccd406c7e6a84d1661d4d2d85dae48cfd8d275dafcd42582a6770597d92ee71", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x27c84448c44f219d1db2897e07359fa508977e34": {"kind": "permit", "domain": {"name": "Heavens", "version": "1", "chainId": 1, "verifyingContract": "0x27c84448c44f219d1db2897e07359fa508977e34"}, "domainSeparator": "0x2fc3ee218211383fd2843ca0b1a1a0419c375358d873e87b0f32de79da4734bc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x83221650e771f54773b32b602cedb705c500a2cc": {"kind": "permit", "domain": {"name": "BLVKHVND", "version": "1", "chainId": 1, "verifyingContract": "0x83221650e771f54773b32b602cedb705c500a2cc"}, "domainSeparator": "0x887bce487bcd3c41c84529a72b034b71134dea89b1c51b1df00a4bcd58a5d3b7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x40eb746dee876ac1e78697b7ca85142d178a1fc8": {"kind": "permit", "domain": {"name": "IAGON", "version": "1", "chainId": 1, "verifyingContract": "0x40eb746dee876ac1e78697b7ca85142d178a1fc8"}, "domainSeparator": "0x9aa5f056751d066943ad8d5c74c0604c97d2b7e57f2942c2429a5b0ee9005118", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6a68de599e8e0b1856e322ce5bd11c5c3c79712b": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x6a68de599e8e0b1856e322ce5bd11c5c3c79712b"}, "domainSeparator": "0x840e36eccbf4a870745bb410670cea924f82f0502be01ca8d22953ae5a9bb5e4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x875773784af8135ea0ef43b5a374aad105c5d39e": {"kind": "permit", "domain": {"name": "Idle", "version": "1", "chainId": 1, "verifyingContract": "0x875773784af8135ea0ef43b5a374aad105c5d39e"}, "domainSeparator": "0x195976971ef367f7c01257e6974524f2fa3141868e87347c8628e1919231a454", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf9c53268e9de692ae1b2ea5216e24e1c3ad7cb1e": {"kind": "permit", "domain": {"name": "Idexo Token", "version": "1", "chainId": 1, "verifyingContract": "0xf9c53268e9de692ae1b2ea5216e24e1c3ad7cb1e"}, "domainSeparator": "0xe05dda4d86e8ee69e9d37d624b70dc6ad83ecfae04dfae559b94a2af74631376", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x63fc0d65b11909465765f5691ac9748abe95a66d": {"kind": "permit", "domain": {"name": "IMPULSE", "version": "1", "chainId": 1, "verifyingContract": "0x63fc0d65b11909465765f5691ac9748abe95a66d"}, "domainSeparator": "0xd99a1d14e3597eff9f2d3e787a0b7a7dc9958abbc7e396355c13d315806e75e0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x04c17b9d3b29a78f7bd062a57cf44fc633e71f85": {"kind": "permit", "domain": {"name": "IMPT", "version": "1", "chainId": 1, "verifyingContract": "0x04c17b9d3b29a78f7bd062a57cf44fc633e71f85"}, "domainSeparator": "0xeab396adbf7fa0ba838fde8388f3eb3e345c2a7cb5cf5ff2719c31aaba4e5172", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1f0f9f2593b23f3667d1fb163a3bb0298c30df63": {"kind": "permit", "domain": {"name": "{∞}", "version": "1", "chainId": 1, "verifyingContract": "0x1f0f9f2593b23f3667d1fb163a3bb0298c30df63"}, "domainSeparator": "0x49f95dda5659d7fb9927f2591714089a575dae116e1bd3284199410e8dcbef4f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba608ab350f1b7602a7a234c70ea5b4a66260b50": {"kind": "permit", "domain": {"name": "Insidor", "version": "1", "chainId": 1, "verifyingContract": "0xba608ab350f1b7602a7a234c70ea5b4a66260b50"}, "domainSeparator": "0x361aa682da66a590f318886c970feada2f92a1cd5a7835db34ba42903e9fa22e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x02d3a27ac3f55d5d91fb0f52759842696a864217": {"kind": "permit", "domain": {"name": "Charged Particles - IONX", "version": "1", "chainId": 1, "verifyingContract": "0x02d3a27ac3f55d5d91fb0f52759842696a864217"}, "domainSeparator": "0xd0a477e0898c4c010915aa45a51913b661b369907bc46a6fc8bb3e7ac8741527", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3abf2a4f8452ccc2cf7b4c1e4663147600646f66": {"kind": "permit", "domain": {"name": "Juicebox", "version": "1", "chainId": 1, "verifyingContract": "0x3abf2a4f8452ccc2cf7b4c1e4663147600646f66"}, "domainSeparator": "0xa9198eedb378f2286e94ddf3dc8dad11d3f7d4a96b86bba08ea7d2525bb5fb2a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe2fc859c838f31c1450448f4fed92e3284ae49e9": {"kind": "permit", "domain": {"name": "Jigen", "version": "1", "chainId": 1, "verifyingContract": "0xe2fc859c838f31c1450448f4fed92e3284ae49e9"}, "domainSeparator": "0x92de1fd95cc8e960beda02e218c0c8ff5852ec4c3fece34a0d7d1c8e102b3a6a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xfc5c549dc834e5e33d4c74a6f685919085d9ab3a1f7e56276208bd90988c42d8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "JulSwap on ETH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xccb38ec48b3fa95d309d85aa4388875d1d5a87ab637db837738dc36ea00d0432", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "AssangeDAO", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x60185892fe17e36fd6b5959ff673b9698150d93f0e6c26efae7a43523f6f9a4e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "HaKibbutz HaMeuchad", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x45edefdff3e4e975b1530130ecb36cf7a8556f896ec147f7c925cc9e04f86854", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x824a50df33ac1b41afc52f4194e2e8356c17c3ac": {"kind": "permit", "domain": {"name": "KickToken", "version": "1", "chainId": 1, "verifyingContract": "0x824a50df33ac1b41afc52f4194e2e8356c17c3ac"}, "domainSeparator": "0x481fa90539aa0032eccd6d1931eabed3af96cf64b04ba375b3d846707e613e36", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x11ea96d1e3d7d2fcc45a793e1a29b1b4b0c9566c": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x11ea96d1e3d7d2fcc45a793e1a29b1b4b0c9566c"}, "domainSeparator": "0xbe32248f73230981f73dba617b68564ddbe9cb6639bf9df54d293cedcc160d33", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1f726ddaaf9abd678858cb840f7a00d427a1f816": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x1f726ddaaf9abd678858cb840f7a00d427a1f816"}, "domainSeparator": "0xe4dfb5a32d74870f6394cbc74307dcacc75ad371b18940b7a0e2dab658f29a1f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x618d62420b3fc761044cebf393283cdf94766f8f": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x618d62420b3fc761044cebf393283cdf94766f8f"}, "domainSeparator": "0xf8903ce9988c6007998fdf6d6da2b8ae458eab5d9bdc020a0ca8eca3c2c6aaf5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x184b6f31aeaa6a19eb207dfea01f4289629e0f0f": {"kind": "permit", "domain": {"name": "FiveKM KMT", "version": "1", "chainId": 1, "verifyingContract": "0x184b6f31aeaa6a19eb207dfea01f4289629e0f0f"}, "domainSeparator": "0x743b5acda3f77a5ba7271e9920ce6ecafd5a73f1aafa445468f7132ab8b09fdf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf0c74886502b2cc7beb7eb9644b5c1461955e3e9": {"kind": "permit", "domain": {"name": "Komodo <PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xf0c74886502b2cc7beb7eb9644b5c1461955e3e9"}, "domainSeparator": "0xbe647df48663961624ea345af9d01d59e3463dc257397e3b4a4a5d7320fa1293", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x049e119413ab1f1b0fd836e657e3e4ba3ad9cab8": {"kind": "permit", "domain": {"name": "KESEF.FINANCE", "version": "1", "chainId": 1, "verifyingContract": "0x049e119413ab1f1b0fd836e657e3e4ba3ad9cab8"}, "domainSeparator": "0x500ff94c0f1c3ed298d5bb98ab6dfabd22e41df2cfb54fe6bf3278812f267494", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcb58418aa51ba525aef0fe474109c0354d844b7c": {"kind": "permit", "domain": {"name": "LaikaProtocol", "version": "1", "chainId": 1, "verifyingContract": "0xcb58418aa51ba525aef0fe474109c0354d844b7c"}, "domainSeparator": "0xf2ab3bc7a0f0d8953ef174dcf29339b58197fb3f07167cfe82a959323019de4e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0bffc5692960bb043d3216839bdd6e5e64ff1b4e": {"kind": "permit", "domain": {"name": "LaikaProtocol", "chainId": 1, "verifyingContract": "0x0bffc5692960bb043d3216839bdd6e5e64ff1b4e"}, "domainSeparator": "0x340e1c8929afd916929aad0acbc05e26beb7b8f9f5d24a4ae99b5b6cd7ca332a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x54aa569005332e4d6f91e27c55307aaef607c0e2": {"kind": "permit", "domain": {"name": "LAND Token", "version": "1", "chainId": 1, "verifyingContract": "0x54aa569005332e4d6f91e27c55307aaef607c0e2"}, "domainSeparator": "0x1514752c1808805debe2d7ee24dc9fc09f5b07c83833382782326f6503a52fab", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa23c1194d421f252b4e6d5edcc3205f7650a4ebe": {"kind": "permit", "domain": {"name": "Launch Block", "version": "1", "chainId": 1, "verifyingContract": "0xa23c1194d421f252b4e6d5edcc3205f7650a4ebe"}, "domainSeparator": "0x6a2ffc39a78b19d20b4a313984715247ec97fe65ca0a96a18b651f90d5c0dc36", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x53263d9ef74db583b15fbc6d5d4e8b83833fa134": {"kind": "permit", "domain": {"name": "LEAP Token", "version": "1", "chainId": 1, "verifyingContract": "0x53263d9ef74db583b15fbc6d5d4e8b83833fa134"}, "domainSeparator": "0xc62b9fe7fc4a3cf29f1e24d2d7c12ba17333bd0729e87256c5521f83c64c5fda", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x63125c0d5cd9071de9a1ac84c400982f41c697ae": {"kind": "permit", "domain": {"name": "LexDAO", "version": "1", "chainId": 1, "verifyingContract": "0x63125c0d5cd9071de9a1ac84c400982f41c697ae"}, "domainSeparator": "0x157d12ca116cf7560c4507c24305f72d6bf7b846f33967ffaf90433655abba17", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7e43fe8ea9a433cb848ddfbbb54c9ad1fcf25856": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "leadgnr.com", "version": "1", "chainId": 1, "verifyingContract": "0x7e43fe8ea9a433cb848ddfbbb54c9ad1fcf25856"}, "domainSeparator": "0x72cfaa18fdbf1fa0a663a9465ebdc4cee6830df19e055b7f0f445855c8189267", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7b71fb77a2f3ca98a0dec88ba7cc53871881809b": {"kind": "permit", "domain": {"name": "Liangxi DAO", "version": "1", "chainId": 1, "verifyingContract": "0x7b71fb77a2f3ca98a0dec88ba7cc53871881809b"}, "domainSeparator": "0xf4540c7adf48c53d35fff6738fd69c7dd9552cc99e5ea48445702baa8b2a0010", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfbfefe2fee2ea07b2a5b367c9a13b0ab43518dcd": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Libra Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "0xfbfefe2fee2ea07b2a5b367c9a13b0ab43518dcd"}, "domainSeparator": "0x84dd5e4667fdf4fbfc36f5be2c027ead333bf55bf70dae235e1229fad41dfd19", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9c38688e5acb9ed6049c8502650db5ac8ef96465": {"kind": "permit", "domain": {"name": "Lif", "version": "1", "chainId": 1, "verifyingContract": "0x9c38688e5acb9ed6049c8502650db5ac8ef96465"}, "domainSeparator": "0x99b8d32b83a6c1a1e8accc7c5169d415db299c6666644fac9e7262a560721ecf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8667625cc5968fcc83e6522be0acd7d85d569048": {"kind": "permit", "domain": {"name": "LightNetwork", "version": "1", "chainId": 1, "verifyingContract": "0x8667625cc5968fcc83e6522be0acd7d85d569048"}, "domainSeparator": "0x651800617c09e4a170a0161da60a783aaa9b0f5c905175003abfcb8f1e374726", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5f69b7ab8f7cab199a310fd5a27b43fef44ddcc0": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x5f69b7ab8f7cab199a310fd5a27b43fef44ddcc0"}, "domainSeparator": "0x0bf1a31790d2917f1959f3a39629b724704d0c3cc99ba0cde2fc9a6182b16bf4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfd0205066521550d7d7ab19da8f72bb004b4c341": {"kind": "permit", "domain": {"name": "Liquidity Incentive Token", "version": "1", "chainId": 1, "verifyingContract": "0xfd0205066521550d7d7ab19da8f72bb004b4c341"}, "domainSeparator": "0xfe394ea3dc522fe9bf3017bcb60631ef5c0068bf30da678dee65242d12ded599", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4071efd58634fa46aa6dab0a2f3ea23d9f5e37b3": {"kind": "permit", "domain": {"name": "LLGT test", "version": "1", "chainId": 1, "verifyingContract": "0x4071efd58634fa46aa6dab0a2f3ea23d9f5e37b3"}, "domainSeparator": "0xe784b7506dda2e1d46e5c5e6dfea746cb1cb03cb69dafb1652962db72bbcaf19", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdec41db0c33f3f6f3cb615449c311ba22d418a8d": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xdec41db0c33f3f6f3cb615449c311ba22d418a8d"}, "domainSeparator": "0x6048c5e9fc1847d7c106742f9ae68df20c5d9944316b48611f6b5cbcfa4e8099", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xce92d0599cf9a6ca3e91e709ccd1c987cc7aa2e0": {"kind": "permit", "domain": {"name": "Logarithmic", "version": "1", "chainId": 1, "verifyingContract": "0xce92d0599cf9a6ca3e91e709ccd1c987cc7aa2e0"}, "domainSeparator": "0x7613a9f8daa44aa813c661a7bd11812afb62dc32c557220ce087d6106eab4334", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x721a1b990699ee9d90b6327faad0a3e840ae8335": {"kind": "permit", "domain": {"name": "LOOT Token", "version": "1", "chainId": 1, "verifyingContract": "0x721a1b990699ee9d90b6327faad0a3e840ae8335"}, "domainSeparator": "0x927bfe4d0cce83a0738d53d4b4172e656aaf8fd320a4cfc309f16ffd2a1ff53e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9da91d7cb48fbbe531458ce79a0b0e9d8dd2ba50": {"kind": "permit", "domain": {"name": "Loot To<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x9da91d7cb48fbbe531458ce79a0b0e9d8dd2ba50"}, "domainSeparator": "0xe4203a99acc4e399bf9ef63e1e356bc364bf79a96f6938293367ab12e4807233", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf5a90648b528bdb12991ab0350db618f461b29c0": {"kind": "permit", "domain": {"name": "LovelyDAO", "version": "1", "chainId": 1, "verifyingContract": "0xf5a90648b528bdb12991ab0350db618f461b29c0"}, "domainSeparator": "0xcee574c2a4797493fb6179d6f682fa43e09d33117e4224b442dce6ee55e3c64c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xce15cb5fe641e0c083bb3e12aa3bf70072d23c1e": {"kind": "permit", "domain": {"name": "LPDAO Token", "version": "1", "chainId": 1, "verifyingContract": "0xce15cb5fe641e0c083bb3e12aa3bf70072d23c1e"}, "domainSeparator": "0x74fc769726ada4cc4d40b04071c6fabf09c0f351e1ad24f22372540cb1488dcb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x652b24e8bfb3a69de5a5181128f313e1a8b33d2d": {"kind": "permit", "domain": {"name": "Loop Protocol", "version": "1", "chainId": 1, "verifyingContract": "0x652b24e8bfb3a69de5a5181128f313e1a8b33d2d"}, "domainSeparator": "0x54f57de3ecc1fe59df793769be09e69d90be82ce85fef32ef55e02298459ffa0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7fe8dac51394157811c71bbf74c133a224a9ff44": {"kind": "permit", "domain": {"name": "LiquidSwap", "version": "1", "chainId": 1, "verifyingContract": "0x7fe8dac51394157811c71bbf74c133a224a9ff44"}, "domainSeparator": "0x99466d3bd3725851a7edbf6e90032bba896eb148a80938ae50781417d6990fed", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6dea81c8171d0ba574754ef6f8b412f2ed88c54d": {"kind": "permit", "domain": {"name": "LQTY", "version": "1", "chainId": 1, "verifyingContract": "0x6dea81c8171d0ba574754ef6f8b412f2ed88c54d"}, "domainSeparator": "0xb792684cdd543bf3e75c7ef4698df91baed0742f0c78c3398904a3542e937594", "domainSeparatorFn": "domainSeparator()", "nonce": "nonces()"}, "0x749b964f3dd571b177fc6e415a07f62b05047da4": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x749b964f3dd571b177fc6e415a07f62b05047da4"}, "domainSeparator": "0x04939708355c0e7be5d06f4e776ea3909a1cfd4977efd40a24199fc077b7cc68", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x32bc49b6c3183437d3e2eaac357415d067e42051": {"kind": "permit", "domain": {"name": "LunacyDAO", "version": "1", "chainId": 1, "verifyingContract": "0x32bc49b6c3183437d3e2eaac357415d067e42051"}, "domainSeparator": "0x9ec1456076b8628c549f1c94e6b72a72ef217baa620be737aa8130102f0467ac", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x71a28feaee902966dc8d355e7b8aa427d421e7e0": {"kind": "permit", "domain": {"name": "LunchDao", "version": "1", "chainId": 1, "verifyingContract": "0x71a28feaee902966dc8d355e7b8aa427d421e7e0"}, "domainSeparator": "0x6ee28409f27712ffe45abba660c480750eef91d145a9c5b8d393f66640652265", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5f98805a4e8be255a32880fdec7f6728c6568ba0": {"kind": "permit", "domain": {"name": "LUSD Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "0x5f98805a4e8be255a32880fdec7f6728c6568ba0"}, "domainSeparator": "0xf85dbb1e9a5845cbb7605972678082c839c02db2df0e2e2eb4977e9744bbcd89", "domainSeparatorFn": "domainSeparator()", "nonce": "nonces()"}, "0x01ba67aac7f75f647d94220cc98fb30fcc5105bf": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x01ba67aac7f75f647d94220cc98fb30fcc5105bf"}, "domainSeparator": "0x14d7a2010a0459fe54d07c2750e795ab347c02e61d84d0a7c0405fa55a06cdb3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3466b4c9e2abc189b59ad64af247619df10c7e7b": {"kind": "permit", "domain": {"name": "Macc Money", "version": "1", "chainId": 1, "verifyingContract": "0x3466b4c9e2abc189b59ad64af247619df10c7e7b"}, "domainSeparator": "0x69676acae7f0b1610b3defa7e1093642e8a724ef3568ddc8bfee63c6259619bb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x745407c86df8db893011912d3ab28e68b62e49b0": {"kind": "permit", "domain": {"name": "MahaDAO", "version": "1", "chainId": 1, "verifyingContract": "0x745407c86df8db893011912d3ab28e68b62e49b0"}, "domainSeparator": "0x70e598d96e715530ae552a7871cdfec3b2fa6a25d6f3847eaff0875007963597", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4af698b479d0098229dc715655c667ceb6cd8433": {"kind": "permit", "domain": {"name": "MaidCoin", "version": "1", "chainId": 1, "verifyingContract": "0x4af698b479d0098229dc715655c667ceb6cd8433"}, "domainSeparator": "0x54ad4c5c3eb6274c0a413d7491d96ee2d0d89bb15e3c81e18a19899a8d3505a1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb29e76bfdf20dfc9721b426ac91f752a493ab99f": {"kind": "permit", "domain": {"name": "MaidCoin", "version": "1", "chainId": 1, "verifyingContract": "0xb29e76bfdf20dfc9721b426ac91f752a493ab99f"}, "domainSeparator": "0xd90463f38123f17b6bcb32dc4abb75d45a2647cfd561148dcf56926f0d76ce71", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6c9bdf986b76211d3951b7b83316cea0ab793398": {"kind": "permit", "domain": {"name": "MakeLove", "version": "1", "chainId": 1, "verifyingContract": "0x6c9bdf986b76211d3951b7b83316cea0ab793398"}, "domainSeparator": "0xaa888609532bb5cbfef24a2fb162f150ffafad7bba482c8cca88943d4d94f372", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "MatrixSwapToken", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1eefb648195b955b5f86ab69489c63842d70019b46c01ee94bc675b3a60840a9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "My Crypto Romance", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xc5d8ed36fee94ee12d46fff7075275cbfbb176d6327cadcc6c936725b0c62187", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Mcr Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x055e3ccb69005c88e81dd042068bbf949395b0da0173966ae0bae3366e79eef7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Memies.io", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0b3d813d2fc5a365fb39d4a95a1e5a22a4810979771599641393e27ebcb69868", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2ebd53d035150f328bd754d6dc66b99b0edb89aa": {"kind": "permit", "domain": {"name": "Metronome2", "version": "1", "chainId": 1, "verifyingContract": "0x2ebd53d035150f328bd754d6dc66b99b0edb89aa"}, "domainSeparator": "0xbd1a2bd7789f83476125d2ce0f99159b59802e82ed9db459563835a17e06845d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8807e69dc04155af64172cd6f0b4738f8068d0d4": {"kind": "permit", "domain": {"name": "Meta Network", "version": "1", "chainId": 1, "verifyingContract": "0x8807e69dc04155af64172cd6f0b4738f8068d0d4"}, "domainSeparator": "0xfdc0351a50402a935dbd5ade7504a807287e7acfde9914a09cb06e357eaf9c81", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x89d5c88b2a871f6b4c4a8d344b88a0b81377df6a97a3274fc2f9c822b21b6708", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "miniETH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x992ca6114e387e7b57c6c5067c9f3fcd2cd6a650d8831ac7e0901f6076bcbe4f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "MemeVerse", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x3b45c8238e00a868c01f8a32fc5b18acf5b13ec73d71cbfbbd396829c78cfc61", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xe74a8076e040bfbe129b49167f9dbd846fc96d9178ae69b1d72a42ae33339acb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "MiND Token", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x3237e0fcec3233c7c20816faad010a4a07b244fb1d90c80268be7207cc7f99ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x21585bbcd5bdc3f5737620cf0db2e51978cf60ac": {"kind": "permit", "domain": {"name": "Manifest", "version": "1", "chainId": 1, "verifyingContract": "0x21585bbcd5bdc3f5737620cf0db2e51978cf60ac"}, "domainSeparator": "0xc46aba3e279dca341a890142e065df767c997963a1cdf827f5f5c4636170b14c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x335e14d18d8a903b782a39059dc35d61b94e1c1b": {"kind": "permit", "domain": {"name": "Mango INU", "version": "1", "chainId": 1, "verifyingContract": "0x335e14d18d8a903b782a39059dc35d61b94e1c1b"}, "domainSeparator": "0x17cd19d0cb6edf4c16e7bcbaf8293063b89b7f7f7b8b063a91f7d8bd1d03e2f7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1ea48b9965bb5086f3b468e50ed93888a661fc17": {"kind": "permit", "domain": {"name": "Moneta", "version": "1", "chainId": 1, "verifyingContract": "0x1ea48b9965bb5086f3b468e50ed93888a661fc17"}, "domainSeparator": "0xf80330d85a220882f824320a4c61ce66e73b563c0e311d21f75f8b9b43993bc6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x99f839134a18dd4c7077b5ae60813de0274611d8": {"kind": "permit", "domain": {"name": "Mongoose DAO", "version": "1", "chainId": 1, "verifyingContract": "0x99f839134a18dd4c7077b5ae60813de0274611d8"}, "domainSeparator": "0x358b712041b6d8fc40da37671b6de26bbc39525db421032b26eecd3d1910d62d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf8640b0b79c236b0c14f67344b4d203ffcedc712": {"kind": "permit", "domain": {"name": "<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xf8640b0b79c236b0c14f67344b4d203ffcedc712"}, "domainSeparator": "0x578ff63ab57774fcee42b88a64ba59cb7ea185fc3333c6e5d7af9833b7ba62ba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Monastery", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x33ae489f9575b6596a9f09c89befbfae257b903c30732b6928763981882c987b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "MOONs on Ethereum", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x027e18e50fca6748275ae5bc42c6ead06dc5b1ac3739ce4d0e53ebc8f946ee01", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "MOONEY", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x03aa9553ebdb1ebd02bddd0525545d25b9ed59f185d1ee50f213b98e1debc9ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Mover", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x2c8775956c37908584e2820fc596a7c88968942b671d3f30c79a8599f676e88b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Methereum", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x82b462e5d0ea7b178a64ca3b17dcb9d072cf4096343fd5b47c898d33f8b652d9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Method", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x9bd9f25970a4079fee76aa55a8e631a8187598812a9f6a28aaf13d09a3382726", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Material", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x4c5d2e5459c52af952b996bf998ec94b0a1933b2b487c72022f8512bd429e653", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Nation3", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x71ff642fa9c3fbbb60179e32161a4af578d5ef2d16a2a17f51de72a2f4da3195", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Nimbus", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xd2f32fdaf59b1e9e24b3e6eea679db97f97fb509e2a20d1891a2494fcde2808d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x71e02750c97a940eccc6f36be05763842b31e1fe": {"kind": "permit", "domain": {"name": "SENDCOIN", "version": "1", "chainId": 1, "verifyingContract": "0x71e02750c97a940eccc6f36be05763842b31e1fe"}, "domainSeparator": "0x6a85f50bd87ef35180de3422a4e78a3afcea9bbc67a37928c2a363c826e4b51f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe69a114289c79818ed2f74408c778aee74315086": {"kind": "permit", "domain": {"name": "Neutrino", "version": "1", "chainId": 1, "verifyingContract": "0xe69a114289c79818ed2f74408c778aee74315086"}, "domainSeparator": "0xdfb105244f70bd6c4bd02fb782268097a0effd8b092cfb1ffcc9fa45dba718d3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x530320c5fcd391c43b8bead2960c32cb43ddd596": {"kind": "permit", "domain": {"name": "Nonefakev", "version": "1", "chainId": 1, "verifyingContract": "0x530320c5fcd391c43b8bead2960c32cb43ddd596"}, "domainSeparator": "0x356dcb3856ed87e5b726b3e624a257cbf6f04298bf774a3c82d348d6481b58bc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xff3ac80c1caa08cbd43a7e90d20c398d54c7342f": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0xff3ac80c1caa08cbd43a7e90d20c398d54c7342f"}, "domainSeparator": "0x6c9174ad4775db30f767de9ead00781064c187f06289ecee49778a8895c2b49e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3c07209ec660fb42249b2b06f0beddb0a272378a": {"kind": "permit", "domain": {"name": "NO1", "version": "1", "chainId": 1, "verifyingContract": "0x3c07209ec660fb42249b2b06f0beddb0a272378a"}, "domainSeparator": "0x8a5d0b3f314ef624fa0e54cb3ab1f180524652d1496a17e84fa00d0f51c34020", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe069af87450fb51fc0d0e044617f1c134163e591": {"kind": "permit", "domain": {"name": "NASDEX Token", "version": "1", "chainId": 1, "verifyingContract": "0xe069af87450fb51fc0d0e044617f1c134163e591"}, "domainSeparator": "0x9d6a86f3bc95507de01667e32d7ec1d11f82574edb6e5190cba8bc29b9cf2949", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x70bef3bb2f001da2fddb207dae696cd9faff3f5d": {"kind": "permit", "domain": {"name": "Ninja Squad Token", "version": "1", "chainId": 1, "verifyingContract": "0x70bef3bb2f001da2fddb207dae696cd9faff3f5d"}, "domainSeparator": "0x7ed265ea54d82c4118585770e03f6e73ede426b37f0b162f26c695a6ca5e52ac", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x16ba8efe847ebdfef99d399902ec29397d403c30": {"kind": "permit", "domain": {"name": "Oh! Finance", "version": "1", "chainId": 1, "verifyingContract": "0x16ba8efe847ebdfef99d399902ec29397d403c30"}, "domainSeparator": "0xa078fc2137bf6f5ae596d5e7ee3346ee69a5df7482f58a5f2159bf456899b42c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x383518188c0c6d7730d91b2c03a03c837814a899": {"kind": "permit", "domain": {"name": "Olympus", "version": "1", "chainId": 1, "verifyingContract": "0x383518188c0c6d7730d91b2c03a03c837814a899"}, "domainSeparator": "0x667c9880c8d353ae824f2326a75216875d996603ed65069e0ec70071b691ecd3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0275e1001e293c46cfe158b3702aade0b99f88a5": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Oiler", "version": "1", "chainId": 1, "verifyingContract": "0x0275e1001e293c46cfe158b3702aade0b99f88a5"}, "domainSeparator": "0x9a1565d0dedc6e3eabe4e00a276580a0edadf5f25aaadcbc53cc3afce2e437ad", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x516d813bc49b0eb556f9d09549f98443acdd7d8f": {"kind": "permit", "domain": {"name": "OKPC GOLD", "version": "1", "chainId": 1, "verifyingContract": "0x516d813bc49b0eb556f9d09549f98443acdd7d8f"}, "domainSeparator": "0xe134a87bef541d91c40b936eee45baca8cb255d4e02bfd10a1c05882daeeb611", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcbb2f7e909a61dec35ebfc8a9c315affface1bf5": {"kind": "permit", "domain": {"name": "OKEX USDT", "version": "2", "chainId": 1, "verifyingContract": "0xcbb2f7e909a61dec35ebfc8a9c315affface1bf5"}, "domainSeparator": "0x8ef48a70e726ef3209858828938c95ce02f00c1f206078b44feaf7e9e710f239", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcf78c7dd70d6f30f6e3609e905e78305da98c863": {"kind": "permit", "domain": {"name": "Ownix", "version": "1", "chainId": 1, "verifyingContract": "0xcf78c7dd70d6f30f6e3609e905e78305da98c863"}, "domainSeparator": "0xe82044634ab085865bf92de3ae4dbc410e9655ff1ef973cc47e316127e437185", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb580f4d0987d78f2f87063c9c30c9cc6e2b62d43": {"kind": "permit", "domain": {"name": "Orwells", "version": "1", "chainId": 1, "verifyingContract": "0xb580f4d0987d78f2f87063c9c30c9cc6e2b62d43"}, "domainSeparator": "0x0531e5a47ab49c20d39bc78f63e1d229a42e99da9674ea40d346f8ecccea8033", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8fcb1783bf4b71a51f702af0c266729c4592204a": {"kind": "permit", "domain": {"name": "OT Aave interest bearing USDC 29DEC2022", "version": "1", "chainId": 1, "verifyingContract": "0x8fcb1783bf4b71a51f702af0c266729c4592204a"}, "domainSeparator": "0x7c261e4f51b52fadace601d79034c1085cf07db13b70e3400a446eb83512d8c8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x010a0288af52ed61e32674d82bbc7ddbfa9a1324": {"kind": "permit", "domain": {"name": "OT Aave interest bearing USDC 30DEC2021", "version": "1", "chainId": 1, "verifyingContract": "0x010a0288af52ed61e32674d82bbc7ddbfa9a1324"}, "domainSeparator": "0xbf0a432eae07513d8c233ddea81aea339a9e320b26fe4479ecbffb7d418acf12", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3d4e7f52efafb9e0c70179b688fc3965a75bcfea": {"kind": "permit", "domain": {"name": "OT Compound Dai 29DEC2022", "version": "1", "chainId": 1, "verifyingContract": "0x3d4e7f52efafb9e0c70179b688fc3965a75bcfea"}, "domainSeparator": "0x661224a8b9ecf405f2dd11bc0a6b9eee12950dbbc7bc77418b517d998718f152", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe55e3b62005a2035d48ac0c41a5a9c799f04892c": {"kind": "permit", "domain": {"name": "OT Compound Dai 30DEC2021", "version": "1", "chainId": 1, "verifyingContract": "0xe55e3b62005a2035d48ac0c41a5a9c799f04892c"}, "domainSeparator": "0xb359a58b771b332d7b22e51b435d7bebe3ce97b151765918595fbdf4eade24df", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x407a3e019c655b779ccd098ff50377e4c5f1c334": {"kind": "permit", "domain": {"name": "OtherDAO", "version": "1", "chainId": 1, "verifyingContract": "0x407a3e019c655b779ccd098ff50377e4c5f1c334"}, "domainSeparator": "0xf19c0ca6c2c94e4e9a9502781fd7b0d41da44e497409136a0640f3e94176c506", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x322d6c69048330247165231eb7848a5c80a48878": {"kind": "permit", "domain": {"name": "OT SushiSwap LP Token 29DEC2022", "version": "1", "chainId": 1, "verifyingContract": "0x322d6c69048330247165231eb7848a5c80a48878"}, "domainSeparator": "0x7f5d4fe592ab92e4c53feb2a3cbe550cabe990377b1ccb8108cb55ae2e44b662", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbf682bd31a615123d28d611b38b0ae3d2b675c2c": {"kind": "permit", "domain": {"name": "OT SushiSwap LP Token 29DEC2022", "version": "1", "chainId": 1, "verifyingContract": "0xbf682bd31a615123d28d611b38b0ae3d2b675c2c"}, "domainSeparator": "0xa54735f8c135bf9f310d77731227b9da6cd09dd344493f15204a123e93189727", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x189564397643d9e6173a002f1ba98da7d40a0fa6": {"kind": "permit", "domain": {"name": "OT wxBTRFLY 21APR2022", "version": "1", "chainId": 1, "verifyingContract": "0x189564397643d9e6173a002f1ba98da7d40a0fa6"}, "domainSeparator": "0xf43abdab3bbf54920002288bbccbe0c83c34dea8db9664caefade06b5e2eacba", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "OVEN.FINANCE", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1a803c65eb0d9c1bc4b87ad1004d78846fd664fae60773560dc4878fe7977599", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WETHUSDC 30-September-2022 900Put USDC Collateral", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xafbe97df242b325a81e4c09809f6eeee06f996f928230cfe308cfa91ab558211", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WETHUSDC 05-August-2022 2350Call WETH Collateral", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x28833b754cd74a924cfe624004a459fe9b6b660d24e47ae200816ed9ae5d866e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WETHUSDC 26-November-2021 4500Call WETH Collateral", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xf469b04cc40e29715b38daa07d01f2d232298119dff1adea265ea68c9696ec1b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WETHUSDC 29-July-2022 2100Call WETH Collateral", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x69204a51274e4400d087a95e5aacc65af977890d197d6255f9c5cbd62d0538d5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WETHUSDC 30-July-2021 2500Call WETH Collateral", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xd3887301722e616458a57beecbb94cda6ab9ca2658a2e132a7f994007349174d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WETHUSDC 30-September-2022 1200Call WETH Collateral", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x19014e6cb2921c501280795465bad49e79c4c1fa9b40386cd1192a464e74d4f3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "OxAI", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x4da90302579e99f2eb5e05b94a8a5ffaeb6d1c727a9f08bcb44fb93e4ef1c86c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "PaintToken", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x7dfd2083b19a7154fdd0dcf48e1ce319d86ac2ef70a343037fc595322b0294d5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3cbb7f5d7499af626026e96a2f05df806f2200dc": {"kind": "permit", "domain": {"name": "PandaDAO", "version": "1", "chainId": 1, "verifyingContract": "0x3cbb7f5d7499af626026e96a2f05df806f2200dc"}, "domainSeparator": "0x906e3943baff317e5a918546099eb6a4471309049b86a60a00075fc1de233779", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9256c9b0bb0cda4b7fbf8212f80184eb1da17e7a": {"kind": "permit", "domain": {"name": "Rebase Panda", "version": "1", "chainId": 1, "verifyingContract": "0x9256c9b0bb0cda4b7fbf8212f80184eb1da17e7a"}, "domainSeparator": "0xbbf033dc9a3873b59a5f66ad9074c767bf72c2fcec6e418530344bd3c8cb39a7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdd9b84f8b435f09baf02cccd746822bb293c3284": {"kind": "permit", "domain": {"name": "YARN", "version": "1", "chainId": 1, "verifyingContract": "0xdd9b84f8b435f09baf02cccd746822bb293c3284"}, "domainSeparator": "0x92c7a06eb7b3c65801ff10c0ab949479e33b1c530786c569861818c98822113c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcff79e48a2dc2f213f23d03b2bffbbc541ceef22": {"kind": "permit", "domain": {"name": "Concave pCNV", "version": "1", "chainId": 1, "verifyingContract": "0xcff79e48a2dc2f213f23d03b2bffbbc541ceef22"}, "domainSeparator": "0x1b2ddddf967c8a1c7f502f1b02167fb6d053a7929915bd05752b4c27c2e5f68f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x528fc7341f43c13268bfb53fc095fa025044f286": {"kind": "permit", "domain": {"name": "PEACEVOID", "version": "1", "chainId": 1, "verifyingContract": "0x528fc7341f43c13268bfb53fc095fa025044f286"}, "domainSeparator": "0x4b58892b0638493023c1a87126e4b2d30a247367960dd78ff0a18ddc510a8166", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9d3ee6b64e69ebe12a4bf0b01d031cb80f556ee4": {"kind": "permit", "domain": {"name": "Wrapped PECO", "version": "1", "chainId": 1, "verifyingContract": "0x9d3ee6b64e69ebe12a4bf0b01d031cb80f556ee4"}, "domainSeparator": "0x0319210dc3cb9554a7676cb28ecc2981df8c60132c8323b2575731d3174f4437", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7a58c0be72be218b41c608b7fe7c5bb630736c71": {"kind": "permit", "domain": {"name": "ConstitutionDAO", "version": "1", "chainId": 1, "verifyingContract": "0x7a58c0be72be218b41c608b7fe7c5bb630736c71"}, "domainSeparator": "0x17aed165d1be7953a8683da1042c33808832fb7e337682061a55abe41db249ab", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6afcff9189e8ed3fcc1cffa184feb1276f6a82a5": {"kind": "permit", "domain": {"name": "PETS Token", "version": "1", "chainId": 1, "verifyingContract": "0x6afcff9189e8ed3fcc1cffa184feb1276f6a82a5"}, "domainSeparator": "0x9a52e8d72702558bad72427bdac7f72c40c40e0fd3acc10116cc778af9516e65", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9107a255c4016ade2c37b2762e7c0d077174a8b6": {"kind": "permit", "domain": {"name": "φ", "version": "1", "chainId": 1, "verifyingContract": "0x9107a255c4016ade2c37b2762e7c0d077174a8b6"}, "domainSeparator": "0x5752c8e648006a1d092515fb90d00d9c2c0ac6d72d54958eb00d56d5d7f6359a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x758b4684be769e92eefea93f60dda0181ea303ec": {"kind": "permit", "domain": {"name": "Phonon DAO", "version": "1", "chainId": 1, "verifyingContract": "0x758b4684be769e92eefea93f60dda0181ea303ec"}, "domainSeparator": "0xc36e70f521752f3062fd45a80c5ddfe606c8038d206d7024c3dda073953e5ef1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x60f5672a271c7e39e787427a18353ba59a4a3578": {"kind": "permit", "domain": {"name": "PIKA", "version": "1", "chainId": 1, "verifyingContract": "0x60f5672a271c7e39e787427a18353ba59a4a3578"}, "domainSeparator": "0x4487a88c73826bd2e07c1515bfac6af8fbc4961ba3415d7c3cfef1c090c94bc9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x02814f435dd04e254be7ae69f61fca19881a780d": {"kind": "permit", "domain": {"name": "Meme Dollar", "version": "1", "chainId": 1, "verifyingContract": "0x02814f435dd04e254be7ae69f61fca19881a780d"}, "domainSeparator": "0x342caece9184b5bbe12002ea99128c736a5af1cf09a8a33fd6bf31cbeb7a6c5f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5aa7f00075d135117667e6ed4938f294c6881ea9": {"kind": "permit", "domain": {"name": "Protocol Inu", "version": "1", "chainId": 1, "verifyingContract": "0x5aa7f00075d135117667e6ed4938f294c6881ea9"}, "domainSeparator": "0xb064fd1da5423a6da484445cd9827d07f4317e64a70e36523ee6d8f9298c2081", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x00ee41dcc47045a85bcf7784264f23a6f8bad35c": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "PITCH - v2", "version": "2", "chainId": 1, "verifyingContract": "0x00ee41dcc47045a85bcf7784264f23a6f8bad35c"}, "domainSeparator": "0xc8745c539fcdd3b3ea2a850d60cac44bdae1f989ced8e66d168e72c2c9ecacb1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1590abe3612be1cb3ab5b0f28874d521576e97dc": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x1590abe3612be1cb3ab5b0f28874d521576e97dc"}, "domainSeparator": "0xb53a0b40682659f73e54a951c0db1e0a5d381b93860cf2f18f392fc5d213a0ca", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2155a67ded74ea71502849dc47cf01e36d8a4cda": {"kind": "permit", "domain": {"name": "Peking University Token", "version": "1", "chainId": 1, "verifyingContract": "0x2155a67ded74ea71502849dc47cf01e36d8a4cda"}, "domainSeparator": "0x11dda41f5bddf9731d99935cb4c6eb6290a44866ee547e921a10ba608de97f09", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd4da2b10cc1982706fde669f9b27afadacfe0715": {"kind": "permit", "domain": {"name": "Platform", "version": "1", "chainId": 1, "verifyingContract": "0xd4da2b10cc1982706fde669f9b27afadacfe0715"}, "domainSeparator": "0xa6e7b1ec35765ff2b6ffcfc7ad7eaa7293920f4ef64c00df87dd22ef0dbd1103", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x130966628846bfd36ff31a822705796e8cb8c18d": {"kind": "permit", "domain": {"name": "PolyPlay", "version": "1", "chainId": 1, "verifyingContract": "0x130966628846bfd36ff31a822705796e8cb8c18d"}, "domainSeparator": "0x45f7d481dc0c001fa2af9a32eede966eb565a3925041f545ad69dfbfb1f17293", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfbf20ba2135cda583757f44aae6547bdbec68463": {"kind": "permit", "domain": {"name": "PulseFeg", "version": "1", "chainId": 1, "verifyingContract": "0xfbf20ba2135cda583757f44aae6547bdbec68463"}, "domainSeparator": "0xaa5de870e317cbef33ca9931bc867a72a0641f82b12b7ec7f87a0b44bbb23590", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe83341b9d5cc95f0e0d6b94ed4820c0f191c51ba": {"kind": "permit", "domain": {"name": "Platinum", "version": "1", "chainId": 1, "verifyingContract": "0xe83341b9d5cc95f0e0d6b94ed4820c0f191c51ba"}, "domainSeparator": "0x506d16c213e839d14ada41aae1a822639733cb1012fbab02cfba2fc9f1127b0e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "PonyDAO", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x5b3929a34cbf4de21fd47bd98550f34d182ee87fcd764d9ed060a7b7b422d99a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "PooTogether Token", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x66881ee5c314d85b33341d37e2569d2cb9e42a96f64f3c42eaa790493bdfc3e9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "PolkaRareToken", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x80337bca48f7c6c95ead273d6d08177d3187a042d3c5dd7b24e80b7d61a1bba0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "PRIMATE", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x8b85057b2108e0cd88ae3916c9df2f1e5f6ba370d24086199ba10ce6bce3163c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfcbcebaada7f2563b40b7ad9695e18b354b2b1ae": {"kind": "permit", "domain": {"name": "Prime Ape Planet", "version": "1", "chainId": 1, "verifyingContract": "0xfcbcebaada7f2563b40b7ad9695e18b354b2b1ae"}, "domainSeparator": "0x7b3463150cfac96a0e1826d7fd2c111f5338d2a38959e9c31b65df6ab5f3414b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7cfea0dd176651e7b5a1ced9c4faf8ee295315fd": {"kind": "permit", "domain": {"name": "PrimeNumbers", "version": "1", "chainId": 1, "verifyingContract": "0x7cfea0dd176651e7b5a1ced9c4faf8ee295315fd"}, "domainSeparator": "0x8cf19b1fade8e658d3d24108eed182f9d4b840826f4001b8731276c717a95c2b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x958adc9a67b867dcaf8f504f922a7909d668122f": {"kind": "permit", "domain": {"name": "Proukrainecoin", "version": "1", "chainId": 1, "verifyingContract": "0x958adc9a67b867dcaf8f504f922a7909d668122f"}, "domainSeparator": "0x363867aa096839460fe976d19c22f0f4f00027affcf930c242aca7c22d5fcd2c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa211f450ce88deb31d3f12ae3c1ebf6b0e55a5d9": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xa211f450ce88deb31d3f12ae3c1ebf6b0e55a5d9"}, "domainSeparator": "0x71b5db740bad4a6889e9b3012da3becab0aa00752c6607dbb0d16ef34635954e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x65fdf4b89cb72e28d31afe71720a5bc3eb662974": {"kind": "permit", "domain": {"name": "PunkCoin", "version": "1", "chainId": 1, "verifyingContract": "0x65fdf4b89cb72e28d31afe71720a5bc3eb662974"}, "domainSeparator": "0xb475b7a367497fe0cf18085786672f7ec35ec3a16fd90ef31891a7522dc0e1c6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc68e539231e6da62513bbdc88e2249f5d012e747": {"kind": "permit", "domain": {"name": "Transgender Rights Coin", "version": "1", "chainId": 1, "verifyingContract": "0xc68e539231e6da62513bbdc88e2249f5d012e747"}, "domainSeparator": "0xa859abe712059c48127ca90333dd42479377ae10ef039de39d8db6688f216333", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xda9fdab21bc4a5811134a6e0ba6ca06624e67c07": {"kind": "permit", "domain": {"name": "QUIDD", "version": "1", "chainId": 1, "verifyingContract": "0xda9fdab21bc4a5811134a6e0ba6ca06624e67c07"}, "domainSeparator": "0x196327f7579b20f8a605cf45d11eab30adcefdd8db343c2bbf2ee10581b123c1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6af36add4e2f6e8a9cb121450d59f6c30f3f3722": {"kind": "permit", "domain": {"name": "Radcoin", "version": "1", "chainId": 1, "verifyingContract": "0x6af36add4e2f6e8a9cb121450d59f6c30f3f3722"}, "domainSeparator": "0xaae814c825dbd4a67d7dc5f938b5d0a824e6ec27867bac8252cbdd55b0d11ae1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6b1871328f3c80f6bd85f1b4b45b67319b2b4c8d": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Radial", "version": "1", "chainId": 1, "verifyingContract": "0x6b1871328f3c80f6bd85f1b4b45b67319b2b4c8d"}, "domainSeparator": "0x8db7e10f36a2229c45624e125ca53a8a1eeb968a920f3393265bc7815f53422e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x94804dc4948184ffd7355f62ccbb221c9765886f": {"kind": "permit", "domain": {"name": "RageToken", "version": "1", "chainId": 1, "verifyingContract": "0x94804dc4948184ffd7355f62ccbb221c9765886f"}, "domainSeparator": "0xa729c6537fe391f6926f8b5e4b68cab4416e42ce94623e67f51a5ab4f3f8e021", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x32e255ed9e9f703563118ab5211ace4f80ce6279": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Rai Reflex Index", "version": "1", "chainId": 1, "verifyingContract": "0x32e255ed9e9f703563118ab5211ace4f80ce6279"}, "domainSeparator": "0x48dcc2f66db71d398b52533b6ebd263121741a5f7bc8fb86e89f576b125778ad", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x03ab458634910aad20ef5f1c8ee96f1d6ac54919": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Rai Reflex Index", "version": "1", "chainId": 1, "verifyingContract": "0x03ab458634910aad20ef5f1c8ee96f1d6ac54919"}, "domainSeparator": "0x85f5ecc29d2ac80177a7103b292e33558c9667ecfe465f47ac4b156eb023567a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba5bde662c17e2adff1075610382b9b691296350": {"kind": "permit", "domain": {"name": "SuperRare", "version": "1", "chainId": 1, "verifyingContract": "0xba5bde662c17e2adff1075610382b9b691296350"}, "domainSeparator": "0xac3640ad7dc974b4b5baa52b9c91978498423cfa50c792aa4ce13e0329e9db68", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4ef32658a6a2a63e42e31b342bd2aebf11a1be09": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x4ef32658a6a2a63e42e31b342bd2aebf11a1be09"}, "domainSeparator": "0xbf9281740a0d6c200c71ed38d25b7616c2f4698ec55857f40c40201d5beda54a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe7ee0b13928afee21a634debbe59693b13658420": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0xe7ee0b13928afee21a634debbe59693b13658420"}, "domainSeparator": "0xc35a2c008e20c7d13e85f01f684f5b4f4ac691be08909bd38f48422c6a13e096", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7570003f85d0c362333e1668bfd638c7f34a259b": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x7570003f85d0c362333e1668bfd638c7f34a259b"}, "domainSeparator": "0x952f7de9e9b6d304ccace5b7622aef0b2100480d398f0a7e95d1d7644a927f49", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb040041fdd14164a566f94f7789fc2e6b5bf9c58": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0xb040041fdd14164a566f94f7789fc2e6b5bf9c58"}, "domainSeparator": "0xd6412e385f32141501b27b0c1fad58a1af13ea43ddb46829f5d615bacde3d4cd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5922a5f93d55aa25b162d407b938be9beacb7585": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x5922a5f93d55aa25b162d407b938be9beacb7585"}, "domainSeparator": "0x881d547817bf6d7844aa3240974bc82124ddf4f359d3e4bdb39e34636a680b75", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x368df85396be56f1d475e5d10339211a77e13c32": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x368df85396be56f1d475e5d10339211a77e13c32"}, "domainSeparator": "0x15d26d80f67974c4a1e508cd98abd236ec50101f4747d10c707f3891f1a2d5bf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x31c1dcf1a63c52418f20b8df4d95411cb76df433": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x31c1dcf1a63c52418f20b8df4d95411cb76df433"}, "domainSeparator": "0xf29fd719ca1d807b278ec3d72f282cf6f834d398aa311f5ef5aa85f0a7076f77", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3368f2b9a0758556de65186eb7dce977543a30ae": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x3368f2b9a0758556de65186eb7dce977543a30ae"}, "domainSeparator": "0x1f1a16525568128b195e093f9319b6c4b13193b83615984ad5dfe24daff61f15", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3238efef52f1141734b17cdefcf70539c01ea7a9": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x3238efef52f1141734b17cdefcf70539c01ea7a9"}, "domainSeparator": "0x53b5790e3116a2b76ef8a462e10f6d8db26fac4e1be71ce1a55bf6e153744a8e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7b73a9e765b947b4eeb05cf7dd17eefaac1b1575": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x7b73a9e765b947b4eeb05cf7dd17eefaac1b1575"}, "domainSeparator": "0x60d6bf18604559d8e53cc49cbd7c3149a1f7a7070d81f37b0c42bad57f5ab50b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd56bc68ef6fce282d54fcd67daa94f284829813b": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0xd56bc68ef6fce282d54fcd67daa94f284829813b"}, "domainSeparator": "0xc2f9bc2e2301df00976d12d97ab4b301121210b452dc89739942ed3fe832383e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x89472e02e3550fd52971ed89a2682613b2c97199": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x89472e02e3550fd52971ed89a2682613b2c97199"}, "domainSeparator": "0x91e3267e3de42940fe675486d45b6ad4847e1eda23d31fdaa7e276dcb5e1d609", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7f701650c116b5a2338b6eed6be0b924fc9a1551": {"kind": "permit", "domain": {"name": "Ruler Protocol rToken", "version": "1", "chainId": 1, "verifyingContract": "0x7f701650c116b5a2338b6eed6be0b924fc9a1551"}, "domainSeparator": "0xd92de49513abf8ee421ecf0d25b83c1344d2dc1c4eb110897a9f1564cbe2bfad", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6f442da588232dc57bf0096e8de48d6961d5cc83": {"kind": "permit", "domain": {"name": "RealToken S 13895 Saratoga st Detroit MI", "version": "2", "chainId": 1, "verifyingContract": "0x6f442da588232dc57bf0096e8de48d6961d5cc83"}, "domainSeparator": "0xa260beb36c62267984e3449c2d5ba47e7a249cefa01e52ba6e6f1142e9ec992c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x315699f1ba88383cff2f2f30fcad187adb2e4d72": {"kind": "permit", "domain": {"name": "RealToken S 14078 Carlisle st Detroit MI", "version": "2", "chainId": 1, "verifyingContract": "0x315699f1ba88383cff2f2f30fcad187adb2e4d72"}, "domainSeparator": "0xb3cb6abda56516e96af1168351137d50fb9c92101dac7b28cece1cb0bb39390c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x41599149f1b52035392402f9e311b1edb0c9f699": {"kind": "permit", "domain": {"name": "RealToken S 14319 Rosemary st Detroit MI", "version": "2", "chainId": 1, "verifyingContract": "0x41599149f1b52035392402f9e311b1edb0c9f699"}, "domainSeparator": "0x6a3d1b9ef44deb690e91da55f1b7ae58499084bb54d35236e2918b1267c9d7ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfe17c3c0b6f38cf3bd8ba872bee7a18ab16b43fb": {"kind": "permit", "domain": {"name": "RealToken S 15777 Ardmore st Detroit MI", "version": "2", "chainId": 1, "verifyingContract": "0xfe17c3c0b6f38cf3bd8ba872bee7a18ab16b43fb"}, "domainSeparator": "0xa4005a42fe5d09a40fd6de81fb6a63978692e8c649ca0917136e085ef22a448c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd9e89bfebae447b42c1fa85c590716ec8820f737": {"kind": "permit", "domain": {"name": "RealToken S 4061 Grand St Detroit MI", "version": "2", "chainId": 1, "verifyingContract": "0xd9e89bfebae447b42c1fa85c590716ec8820f737"}, "domainSeparator": "0x7042307360a3068ed025d5622a76ab927e9f82067276cb85a739c496140636ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x96700ffae33c651bc329c3f3fbfe56e1f291f117": {"kind": "permit", "domain": {"name": "RealToken S 4380 Beaconsfield st Detroit MI", "version": "2", "chainId": 1, "verifyingContract": "0x96700ffae33c651bc329c3f3fbfe56e1f291f117"}, "domainSeparator": "0x457499223a686cca5c082145b11af0435f0a1966dffe4ebb37892f916d7aefb9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x78b5c6149c87c82edcffc73c230395abbc56ddd5": {"kind": "permit", "domain": {"name": "Regular Token", "version": "1", "chainId": 1, "verifyingContract": "0x78b5c6149c87c82edcffc73c230395abbc56ddd5"}, "domainSeparator": "0x4d01c6a419a5a6877152997b256202fa17ed33f30238210d27c9667503444299", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5d843fa9495d23de997c394296ac7b4d721e841c": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x5d843fa9495d23de997c394296ac7b4d721e841c"}, "domainSeparator": "0x36a50b32389720cc61797bc926b2da9b46941e5d8fdb55d30a9be38e1a82172f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renBCH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1af104b0d6dfeaae14d1e7876def608e1352f218002ca21af03a52f9485d6532", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renBTC", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xa66c46a3743f744c15bb31450162d77172718e42ab2e70f5e8ce17fba8aafab1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renDGB", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x416fdf2b0fef4c86bdc9cacd81fc577958fab0df433723955006757a13e5ee4a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renDOGE", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x79051d31b32f61e7434461d46fadfc73e9613e76db647dfcc15d81420ae39526", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd5147bc8e386d91cc5dbe72099dac6c9b99276f5": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renFIL", "version": "1", "chainId": 1, "verifyingContract": "0xd5147bc8e386d91cc5dbe72099dac6c9b99276f5"}, "domainSeparator": "0xde1f17624f1c74cc96541c13177311dc8c5d4e926be65767e645ed1e0744078b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x52d87f22192131636f93c5ab18d0127ea52cb641": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renLUNA", "version": "1", "chainId": 1, "verifyingContract": "0x52d87f22192131636f93c5ab18d0127ea52cb641"}, "domainSeparator": "0xa4f161aa60f432adcd972efb44602eccc4dedbc7442f81a9234d5892ebe9cec3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "renZEC", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x892f05b788a5e7cfb15837b2ad86472135bcb105a095a91f299a652cce778bdc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "StakeWise Reward ETH2", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x19590e845458066b8119bcb94d5875f2d80e466302992f0c26e3e1e06f3c4d1a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "REU Mining", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1acb793b17468741e537bc22e8273c06b4f06e312bb6f3e57213c265f798382b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "REU Mining", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x47426de08ea709badaec185ded1f19e546f0063942646249a94a2285701c66bd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5aaef4659c683d2b00ef86aa70c6ab2e5a00bcc7": {"kind": "permit", "domain": {"name": "RICH", "version": "1", "chainId": 1, "verifyingContract": "0x5aaef4659c683d2b00ef86aa70c6ab2e5a00bcc7"}, "domainSeparator": "0xfd6b0f0fbb6e8e9a77e20533b1016e689d9d19e129457c89b11ee09059ce5a9e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0cd022dde27169b20895e0e2b2b8a33b25e63579": {"kind": "permit", "domain": {"name": "EverRise", "version": "1", "chainId": 1, "verifyingContract": "0x0cd022dde27169b20895e0e2b2b8a33b25e63579"}, "domainSeparator": "0xa4c7b00ef0bf3efe2e6311e4fc083e21c772a7665bd4a8a3b99c89611ca78d07", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7616f89f2537e32454c069fb95d74bc9a8aaa5ae": {"kind": "permit", "domain": {"name": "Rockets", "version": "1", "chainId": 1, "verifyingContract": "0x7616f89f2537e32454c069fb95d74bc9a8aaa5ae"}, "domainSeparator": "0x7da8471552fb3b15e13373b21a4bb50e291c369d68c42042b797f7565363f9e2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x68ed268ec325b9e05ad25fcd82c889d9716a6edc": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x68ed268ec325b9e05ad25fcd82c889d9716a6edc"}, "domainSeparator": "0x2bd7ffd98fbbcd97b40c9f174963ad32a324d1ac987e5ca7fe6c767169ecf605", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2c5e6ffe9f7f161c15f78cd67d79c33a7c8d84b5": {"kind": "permit", "domain": {"name": "Roborovski NameChangeToken", "version": "1", "chainId": 1, "verifyingContract": "0x2c5e6ffe9f7f161c15f78cd67d79c33a7c8d84b5"}, "domainSeparator": "0x399affcddd5c13f108422b1f1da315fad24b81c194c2744b4feccd1d5bdf6a3b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8ebfe1e17ea41f8b737e4f7670885b2278b1cc32": {"kind": "permit", "domain": {"name": "Chadverse Token", "version": "1", "chainId": 1, "verifyingContract": "0x8ebfe1e17ea41f8b737e4f7670885b2278b1cc32"}, "domainSeparator": "0x79490ba3816ee964157e22bebfd854b22f2188ad8f377987a5ee3391af139bae", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xed025a9fe4b30bcd68460bca42583090c2266468": {"kind": "permit", "domain": {"name": "Ripio Coin", "version": "1", "chainId": 1, "verifyingContract": "0xed025a9fe4b30bcd68460bca42583090c2266468"}, "domainSeparator": "0xac5cb6a5e304870aa7850a56d7ecaae3e8d2c9ddf92bcb0ab9b2fdf9fdb76ed1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x320623b8e4ff03373931769a31fc52a4e78b5d70": {"kind": "permit", "domain": {"name": "Reserve Rights", "version": "1", "chainId": 1, "verifyingContract": "0x320623b8e4ff03373931769a31fc52a4e78b5d70"}, "domainSeparator": "0x9f8c51bd66d73da0099b2b055d0e925e241e9ea0e86b4ab43836e79a2a92a6df", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8adf82dd1e0dbce5d92cc481e59659532cbbfa9c": {"kind": "permit", "domain": {"name": "RUBC Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "0x8adf82dd1e0dbce5d92cc481e59659532cbbfa9c"}, "domainSeparator": "0xd12b60fbbd31fdf1bf3c837ab597b19de420205c7480bdc36e805342cae7f227", "domainSeparatorFn": "domainSeparator()", "nonce": "nonces()"}, "0x0c74e87c123b9a6c61c66468f8718692c1397a53": {"kind": "permit", "domain": {"name": "RUSH", "version": "1", "chainId": 1, "verifyingContract": "0x0c74e87c123b9a6c61c66468f8718692c1397a53"}, "domainSeparator": "0xd1ed0248dd55cbdae5996b74871cf8f3fb0d9b9332664ae2511050dbbfcdaa78", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe9f84de264e91529af07fa2c746e934397810334": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0xe9f84de264e91529af07fa2c746e934397810334"}, "domainSeparator": "0x25694c9963cfc2f5478989907422239e8947aeae0f940ac9620ef7671b46720c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2ba282bc146bd8451afb662d553e4480f904393d": {"kind": "permit", "domain": {"name": "Sango Coin", "version": "1", "chainId": 1, "verifyingContract": "0x2ba282bc146bd8451afb662d553e4480f904393d"}, "domainSeparator": "0x6c6fdaf7b42f585d1cdf1c638dc58f38daa94bd8f90869b1e7276c6a754d2b98", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf3dce50625cc3dd2c335326be679a0705021e017": {"kind": "permit", "domain": {"name": "SBFINU", "version": "1", "chainId": 1, "verifyingContract": "0xf3dce50625cc3dd2c335326be679a0705021e017"}, "domainSeparator": "0xb7704eaf472434a54f7c83ae5eacee520af3744f4bd2dd49e4917601b920383e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x30d20208d987713f46dfd34ef128bb16c404d10f": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x30d20208d987713f46dfd34ef128bb16c404d10f"}, "domainSeparator": "0xa65342184936cd4fbaea8752676f1a7d4b60638c1a06116c8da7adcb5d74fade", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Sda", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x10d612de5b2e3d6690a6165234b53bc63540a92f5df1b46478356b490c9023b8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "StakeWise Staked ETH2", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xfd424f1b64131f15ddc674aaf87339889295d82ee5246a7c49f3b283d651df50", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Sonic Finance", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x6902dbcd6a37524112d22656e55dc41719a1adec0bdd06d57f7857d66b3a458b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Staked Frax Ether", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xe8254029df4a5fbfcf2de75f8fcf68b1060354a363693b1507e1197cc3dcc29b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "sfrxETH-stETH-rETH StablePool", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x9cbd04c3aba61e134b61f6306fac1c12ddcda7d14bbb2e8f64ea5625ca268ce6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Scorefam", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x3818fb011b85e7dc8bc8628f1d516d5946778f95dd7386ee45273293fcd411f2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "SeedifyFund", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x5ef8b7124e6b8d1e770e5c2786bd8f3d7ff926297498de13a4e84661c19fcbf4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0aa9d1c726acbfa1ef8683a6b105ef46c5889a7c": {"kind": "permit", "domain": {"name": "Staked Gaas", "version": "1", "chainId": 1, "verifyingContract": "0x0aa9d1c726acbfa1ef8683a6b105ef46c5889a7c"}, "domainSeparator": "0x82901023830e2b39ebb66079efb4695633f32971680371497ea6ab82c7c31bd2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x24c19f7101c1731b85f1127eaa0407732e36ecdd": {"kind": "permit", "domain": {"name": "Sharedstake.finance", "version": "1", "chainId": 1, "verifyingContract": "0x24c19f7101c1731b85f1127eaa0407732e36ecdd"}, "domainSeparator": "0xbea2398a018e9e0309b1d4dc2b6b110548dcf6bd5c7ca6a5174d7ec295047c31", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x232afce9f1b3aae7cb408e482e847250843db931": {"kind": "permit", "domain": {"name": "Shark DAO", "version": "1", "chainId": 1, "verifyingContract": "0x232afce9f1b3aae7cb408e482e847250843db931"}, "domainSeparator": "0xe1d7c9dba3cd52d366de58e063c2814a0c656df0535dfe912975d709e6124157", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcb9f441ffae898e7a2f32143fd79ac899517a9dc": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xcb9f441ffae898e7a2f32143fd79ac899517a9dc"}, "domainSeparator": "0xafdc17f69789a3c9560c3e3738aaccdf7c1dea92a4f8574246859cabb7277384", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba5db223543a20972e7417b91323475b228dceb3": {"kind": "permit", "domain": {"name": "Shibarium Network", "version": "1", "chainId": 1, "verifyingContract": "0xba5db223543a20972e7417b91323475b228dceb3"}, "domainSeparator": "0x332d16aa9754d65d369b752a65964ab6884ee436abc2620abd4f36fa51a64d61", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xad996a45fd2373ed0b10efa4a8ecb9de445a4302": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>um", "version": "1", "chainId": 1, "verifyingContract": "0xad996a45fd2373ed0b10efa4a8ecb9de445a4302"}, "domainSeparator": "0x3bc533f9a1c56a0faf3545ac22aa9271273eda64e4561106bbcbeef67f4309d3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa09c0e98a252965a2b6bf2994e2d2c020af0f89a": {"kind": "permit", "domain": {"name": "ShibOHM", "version": "1", "chainId": 1, "verifyingContract": "0xa09c0e98a252965a2b6bf2994e2d2c020af0f89a"}, "domainSeparator": "0x29232243b10201d553c5f999733469d4fcdfbf2ec11871ae2bd1b52503a57dc0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf9abaeec0334fd30804446db7423abe0c02ef47d": {"kind": "permit", "domain": {"name": "SHO Token", "version": "1", "chainId": 1, "verifyingContract": "0xf9abaeec0334fd30804446db7423abe0c02ef47d"}, "domainSeparator": "0x7876da59a43c6678d5ab99589f9c26433a88e449d1a1450342a750de6d968a2a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x947d5acea7b613d46d119ca38e49d3d72383c113": {"kind": "permit", "domain": {"name": "Sigma DAO", "version": "1", "chainId": 1, "verifyingContract": "0x947d5acea7b613d46d119ca38e49d3d72383c113"}, "domainSeparator": "0xac44cb88a4aec98429b135550b5f68e113ac4fe31ba66f26609511caead503ee", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x804a4f2705f7bd08b1d84ae8698014a18c708dbc": {"kind": "permit", "domain": {"name": "Sins", "version": "1", "chainId": 1, "verifyingContract": "0x804a4f2705f7bd08b1d84ae8698014a18c708dbc"}, "domainSeparator": "0x2ad8d997eaa8a6aa8d5b579d1be1ccd4f0dabe89fd748674d86a217f1d73477d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb33440566865f4433e28f90f61f0a8ec334b761e": {"kind": "permit", "domain": {"name": "Sins", "version": "1", "chainId": 1, "verifyingContract": "0xb33440566865f4433e28f90f61f0a8ec334b761e"}, "domainSeparator": "0x35ea11ea9724205cb124662014988aaca1e5d350d24532253b38a2c44de6faa8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd71eea2bb8f54cd67ac26be39fd41ef13ebd0ba8": {"kind": "permit", "domain": {"name": "Stable INR", "version": "1", "chainId": 1, "verifyingContract": "0xd71eea2bb8f54cd67ac26be39fd41ef13ebd0ba8"}, "domainSeparator": "0xdc8f28a3857de65183aa73b6baccae0b100e3e3f9121c5a97126c039351ffcc6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x32f37d20e837d699bdc94af8bd1becef0666bf2d": {"kind": "permit", "domain": {"name": "Skynet", "version": "1", "chainId": 1, "verifyingContract": "0x32f37d20e837d699bdc94af8bd1becef0666bf2d"}, "domainSeparator": "0x23a9ff94e473ed326704d1b6fa54c71f80f93446d6a300a8d472aa8d9c553690", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdb795ef4bf54401ba954109681587e5f497ea0a2": {"kind": "permit", "domain": {"name": "slothclimb", "version": "1", "chainId": 1, "verifyingContract": "0xdb795ef4bf54401ba954109681587e5f497ea0a2"}, "domainSeparator": "0xb3d17a1e2387f1986780a3e6e18d3bd251d235bbbfa59f1ff8924e08bf319d6b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6a091a3406e0073c3cd6340122143009adac0eda": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x6a091a3406e0073c3cd6340122143009adac0eda"}, "domainSeparator": "0x531ffd411cf382afac78ebb1b39afa797060f077da03aa7e077ee1c049c4e5cf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x68c6d02d44e16f1c20088731ab032f849100d70f": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x68c6d02d44e16f1c20088731ab032f849100d70f"}, "domainSeparator": "0xc08a98354636c489e386bc5b4bf99ec73f821be29a1d1b56130b76f0c2ebdfe1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x58dc5a51fe44589beb22e8ce67720b5bc5378009": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x58dc5a51fe44589beb22e8ce67720b5bc5378009"}, "domainSeparator": "0x97c5e1f7a445de442abcc5abf5f9a452a4d88c807754cb10b35d32c7e0bf74f1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x518d6ce2d7a689a591bf46433443c31615b206c5": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x518d6ce2d7a689a591bf46433443c31615b206c5"}, "domainSeparator": "0xd5cdcb4e34a8d8585baaaae339abf30d0e82fb513c53bd3ff9bcc0f5db132f82", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4fb3cae84a1264b8bb1911e8915f56660ec8178e": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x4fb3cae84a1264b8bb1911e8915f56660ec8178e"}, "domainSeparator": "0x29a27f64c1a30e4c460c81b993ac753dffb54dc6ed5125372f761551fffbf021", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xceff51756c56ceffca006cd410b03ffc46dd3a58": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xceff51756c56ceffca006cd410b03ffc46dd3a58"}, "domainSeparator": "0x1e9ab957b7b101583c93598ad8c108399441bfb2fe62ca387b35365d265a35dc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc926990039045611eb1de520c1e249fd0d20a8ea": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xc926990039045611eb1de520c1e249fd0d20a8ea"}, "domainSeparator": "0xf5f0a4e003812b9c01cf6340026123b6772217576a3985728e0b192e6cea0014", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd75ea151a61d06868e31f8988d28dfe5e9df57b4": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xd75ea151a61d06868e31f8988d28dfe5e9df57b4"}, "domainSeparator": "0x3360c27095ef0a8e85af110b70d9d002df69ce83bee4c467bfba6ff3f02eef4b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcb2286d9471cc185281c4f763d34a962ed212962": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xcb2286d9471cc185281c4f763d34a962ed212962"}, "domainSeparator": "0x8fff139dc6cc035f4caac3b9974c0cf10eea574822d4ae3e9792587b2e454f80", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbded49a446de07bb9c1568a4da975d2d319d0d55": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xbded49a446de07bb9c1568a4da975d2d319d0d55"}, "domainSeparator": "0x3a9be543675c86ab0c6a9d4fb9f1c1e693a2aaae193d8efa07000f0ddd87c4ec", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbcd6a2ddafbaa7f424698ed69e717c0c0f1e99bf": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xbcd6a2ddafbaa7f424698ed69e717c0c0f1e99bf"}, "domainSeparator": "0x82149529acb948201c83c20e15ecdbeadb809ad1ea7d89eaa0beb881a9928f8a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc5fa164247d2f8d68804139457146efbde8370f6": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xc5fa164247d2f8d68804139457146efbde8370f6"}, "domainSeparator": "0x9b7802fa72ae117edd2dae9c5e572a0b5bcf49e27139c5caba071c56e0bcffe3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc2b0f2a7f736d3b908bdde8608177c8fc28c1690": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xc2b0f2a7f736d3b908bdde8608177c8fc28c1690"}, "domainSeparator": "0x9f1476844f147d031cbd4e8ee3bd7c9968c3c37a3c43d1c2d7c2c7d8105e9fd4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc9cb53b48a2f3a9e75982685644c1870f1405ccb": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xc9cb53b48a2f3a9e75982685644c1870f1405ccb"}, "domainSeparator": "0xfbc4cf9c8098c6cb0d94d98edf7ced40d8a5385caf274733ec8c0273d88bc2e4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb84c45174bfc6b8f3eaecbae11dee63114f5c1b2": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xb84c45174bfc6b8f3eaecbae11dee63114f5c1b2"}, "domainSeparator": "0x2af8aff39e49c4f722a66e8b01e4080be07e869a2db2cfc74c8e36630bdf4162", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba13afecda9beb75de5c56bbaf696b880a5a50dd": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xba13afecda9beb75de5c56bbaf696b880a5a50dd"}, "domainSeparator": "0x4fd9215eef437fb033e98c9ab28a787b4702f7f474785fb67d04d12d5849af56", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcfd111c4c0daeae6351bcdc80a993e78923845ab": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xcfd111c4c0daeae6351bcdc80a993e78923845ab"}, "domainSeparator": "0xbc218922fc07bd2c02afebe261887c24dda8064a0fb1aebe91d2821e0a4e0789", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc40d16476380e4037e6b1a2594caf6a6cc8da967": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xc40d16476380e4037e6b1a2594caf6a6cc8da967"}, "domainSeparator": "0x5b91aa350fa3ea1ba5057e110ca8ffce704aceb87e2331fdddf43069158121a2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd3f85d18206829f917929bbbf738c1e0ce9af7fc": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xd3f85d18206829f917929bbbf738c1e0ce9af7fc"}, "domainSeparator": "0xe9f7a47d4b49c6e05d8ed14257595f3235acdc8008f81796195b24a6451e3651", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd453d48aca428fd718bc0e1ad238bfc009eff1d0": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xd453d48aca428fd718bc0e1ad238bfc009eff1d0"}, "domainSeparator": "0x930d94cfa859868c493b830921cd8282f77de35a08fc20be9dc70901ec35096e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc3d03e4f041fd4cd388c549ee2a29a9e5075882f": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xc3d03e4f041fd4cd388c549ee2a29a9e5075882f"}, "domainSeparator": "0x276e7ccd58c8b3a7ce407c1c5baf91de040af3849b9846207aeb147db1f7ed82", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x47d9e3f2ddae87abe831521cf5a9eb7b71ccf3ef": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x47d9e3f2ddae87abe831521cf5a9eb7b71ccf3ef"}, "domainSeparator": "0xa683b28b27d4b88371ae4f7975b970f35bca5101edef98cccb7acbeca6facdcf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x35a0d9579b1e886702375364fe9c540f97e4517b": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x35a0d9579b1e886702375364fe9c540f97e4517b"}, "domainSeparator": "0x79ac9f90b70b2cc1bcd7e3f0567a89000c8b4f240914460d8d0f78542e957632", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x378b4c5f2a8a0796a8d4c798ef737cf00ae8e667": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x378b4c5f2a8a0796a8d4c798ef737cf00ae8e667"}, "domainSeparator": "0x1d0f4ee1ca83486e81587a7b5c60c9318101189dce005fdd43fa749ddb46d4b2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x31503dcb60119a812fee820bb7042752019f2355": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x31503dcb60119a812fee820bb7042752019f2355"}, "domainSeparator": "0xa3f70c7d9857f6d73b5611316436e36f5bbac6f37456c5529bb26513a243c037", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x364248b2f1f57c5402d244b2d469a35b4c0e9dab": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x364248b2f1f57c5402d244b2d469a35b4c0e9dab"}, "domainSeparator": "0xa113691e9c8bd66a3c8975500adbb33cf2ae4808e80a51780fa074a78f820867", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x397ff1542f962076d0bfe58ea045ffa2d347aca0": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x397ff1542f962076d0bfe58ea045ffa2d347aca0"}, "domainSeparator": "0xbc49fc430bc86299bccfb8106eb53ad9a8355f9ff37a9fb4b745d4b992ed74e9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x26d8151e631608570f3c28bec769c3afee0d73a3": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x26d8151e631608570f3c28bec769c3afee0d73a3"}, "domainSeparator": "0xd952dc22ae27b46c16365405431b77dcf6eef18dab79841c0e15e088141a508b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe4455fdec181561e9ffe909dde46aaeaedc55283": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xe4455fdec181561e9ffe909dde46aaeaedc55283"}, "domainSeparator": "0x2e8a446cc5a9746133be46eb471bde1109c9aaa2aa7c4e600ac3889d9d0703d6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfb3cd0b8a5371fe93ef92e3988d30df7931e2820": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xfb3cd0b8a5371fe93ef92e3988d30df7931e2820"}, "domainSeparator": "0xa8b93d982636330e157a15b329f1337e1d14913ef8956669ec240125f34dd2cb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xeefa3b448768dd561af4f743c9e925987a1f8d09": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xeefa3b448768dd561af4f743c9e925987a1f8d09"}, "domainSeparator": "0x7694057652cbf67fef6a16fefa3194852fffacc29c5664d3feb08b8d7ba00dd1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdafd66636e2561b0284edde37e42d192f2844d40": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xdafd66636e2561b0284edde37e42d192f2844d40"}, "domainSeparator": "0xa146d2d3800f3fe9559a436a400d556d0b94adf1c687c80f4da82f93c7f2f847", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe7689b2c21242e07870aaa0ffee1ec11833d5e24": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xe7689b2c21242e07870aaa0ffee1ec11833d5e24"}, "domainSeparator": "0xaca41f16a087091ef8e1be5f619433c184dc1f58fb47d774e51f342574a6f13f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf1f85b2c54a2bd284b1cf4141d64fd171bd85539": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xf1f85b2c54a2bd284b1cf4141d64fd171bd85539"}, "domainSeparator": "0x2e9391bba64aaf04cd8be71ef0b038cabb81268b95ad0d1c3726e04ee74063c7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe5f06db4f3473e7e35490f1f98017728496fe81e": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xe5f06db4f3473e7e35490f1f98017728496fe81e"}, "domainSeparator": "0xeeb7c8d6d61e3ae34e0ec3e554376b3e4667c83d2c753fa2fcf06c28ab36ba45", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdd77c93199064a53e1db19ee0930bcdf7c9999f4": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xdd77c93199064a53e1db19ee0930bcdf7c9999f4"}, "domainSeparator": "0x5d5d40c125ff9e88f7a26a5f63be70389de741b77c0a0187bb3c0795cc965a27", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfceaaf9792139bf714a694f868a215493461446d": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xfceaaf9792139bf714a694f868a215493461446d"}, "domainSeparator": "0xfb3dcc50b344452a70a8ce35edfdb6d34b6b541a4bbe85cf849649ad656c9eee", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfcfee7769c012578877dc787a5d9a339cc5920a1": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xfcfee7769c012578877dc787a5d9a339cc5920a1"}, "domainSeparator": "0xbecaa0d397c355c72e2941e3d7255536930a832c091033e589d7a493d9ec55da", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x201e6a9e75df132a8598720433af35fe8d73e94d": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x201e6a9e75df132a8598720433af35fe8d73e94d"}, "domainSeparator": "0x49c374e72e59460cddc60725aa43275f1fc4e6cc496220694e63c3f785c16c86", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0cd198a5f6cc84ca8499dd97a6c1a1c4b4a12da1": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x0cd198a5f6cc84ca8499dd97a6c1a1c4b4a12da1"}, "domainSeparator": "0x03ef5fefbfb967c49b8663af972f4501ac714abaa32c5e02a6041fbcc63ffb64", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x110492b31c59716ac47337e616804e3e3adc0b4a": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x110492b31c59716ac47337e616804e3e3adc0b4a"}, "domainSeparator": "0x0999070509b759d1c1682d6c8fe909cf781b31b07fd595cbb564e176e41667a1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x06da0fd433c1a5d7a4faa01111c044910a184553": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x06da0fd433c1a5d7a4faa01111c044910a184553"}, "domainSeparator": "0xdf7761d4488e6868d768bf6bdd4f46cc2d552131724eae9c0ce02d6280ca0049", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x066f3a3b7c8fa077c71b9184d862ed0a4d5cf3e0": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x066f3a3b7c8fa077c71b9184d862ed0a4d5cf3e0"}, "domainSeparator": "0x796e4e7fd477b1fc17a39a15492c837ae0c8b8b92a5c3303576f20a28bd1c24f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x00c70e8b3c9d0e0adb85993382abaae2a11c5d96": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x00c70e8b3c9d0e0adb85993382abaae2a11c5d96"}, "domainSeparator": "0x98fefa7190111a2b3541ab72be779739552a7f434f703f644c0a7828e3158347", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x10b47177e92ef9d5c6059055d92ddf6290848991": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x10b47177e92ef9d5c6059055d92ddf6290848991"}, "domainSeparator": "0x385a00ff693c2164bcd265a9e89a36b2d054a0dc4a49b4e1d93a6668f60820f1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x02c6260ce42ea5cd055911ed0d4857ecd4583740": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x02c6260ce42ea5cd055911ed0d4857ecd4583740"}, "domainSeparator": "0x173cb5b1497fefea275eda1d269a549dc4cb7fdfb7e17b157709b1c58d80d30c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x033ecd066376afec5e6383bc9f1f15be4c62dc89": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x033ecd066376afec5e6383bc9f1f15be4c62dc89"}, "domainSeparator": "0xc4d444b48c5ded714136b766d095a2af8a0f54b4c3e2d8505bce9846bd82d7ad", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x088ee5007c98a9677165d78dd2109ae4a3d04d0c": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x088ee5007c98a9677165d78dd2109ae4a3d04d0c"}, "domainSeparator": "0x9bae441bf1a98707ff0ea294de9d457a5b2c4c394f07cedafed36b18038b4036", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x83b04af7a77c727273b7a582d6fda65472fcb3f2": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x83b04af7a77c727273b7a582d6fda65472fcb3f2"}, "domainSeparator": "0x965f8bfa3c982d98e5c5191d632e0dba51186a43ef70f5977050ed006e5e3030", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8b00ee8606cc70c2dce68dea0cefe632cca0fb7b": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x8b00ee8606cc70c2dce68dea0cefe632cca0fb7b"}, "domainSeparator": "0x571c4acd0e6c58f9ae87d1c7eb80bd23bec1a731712f538c3aef52e07d3843e7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x850fe284f917eb6dfcfe452348bead903aa63073": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x850fe284f917eb6dfcfe452348bead903aa63073"}, "domainSeparator": "0x8193d1e0d77d9096d8c10d66c6f173a5bf1e36a7e058f1dd446952ae0a4ff51f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x804be24f625c7e23edd9fa68e4582590c57ad2b3": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x804be24f625c7e23edd9fa68e4582590c57ad2b3"}, "domainSeparator": "0xa1ad38c26480d4ae759bff763fbbd929b260e24e2f7ac26ce396a092e564c964", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x715134a16acb73c86e81df5542e1cf759eeb6fc7": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x715134a16acb73c86e81df5542e1cf759eeb6fc7"}, "domainSeparator": "0xfc5840b167d7ef5579ebc7cfd2b17488a32017e1151c554148b623bbca083dfc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6f58a1aa0248a9f794d13dc78e74fc75140956d7": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x6f58a1aa0248a9f794d13dc78e74fc75140956d7"}, "domainSeparator": "0x14dcb844f19969ae6c295d332f46831ce71903c25fb156b1c7137fc81d6e01d2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x795065dcc9f64b5614c407a6efdc400da6221fb0": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x795065dcc9f64b5614c407a6efdc400da6221fb0"}, "domainSeparator": "0xb0f840cca00be9286ba53b725af99b810db67d14efc2179856a4536479a4291a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7c5bbe30b5b1c1abaa9e1ee32e04a81a2b20a052": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x7c5bbe30b5b1c1abaa9e1ee32e04a81a2b20a052"}, "domainSeparator": "0x22ad0dd9703f0ab1786b958d9884535421e103f4c11d87594e0a9623f7c82270", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa1d7b2d891e3a1f9ef4bbc5be20630c2feb1c470": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xa1d7b2d891e3a1f9ef4bbc5be20630c2feb1c470"}, "domainSeparator": "0xf4c4a29b84bc27507f2ac1bb8d185504247da14e8441b914a82220ee3e4a04d3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9360b76f8f5f932ac33d46a3ce82ad6c52a713e5": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x9360b76f8f5f932ac33d46a3ce82ad6c52a713e5"}, "domainSeparator": "0xf77631b8ec0f77212b6f2a421cad45af6d58d4fb2821e4cf2bc604db6cf91f3d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xaf988aff99d3d0cb870812c325c588d8d8cb7de8": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xaf988aff99d3d0cb870812c325c588d8d8cb7de8"}, "domainSeparator": "0x40114ce963e6ccbef37970e50dd61e81e7b08f0eb9a491530249a2cc78594ed6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa73df646512c82550c2b3c0324c4eedee53b400c": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xa73df646512c82550c2b3c0324c4eedee53b400c"}, "domainSeparator": "0x90d6abbf78c80b6896cb28e8b0e08bd86f2f8a260ce67a8019b5fd079349dac7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9cd028b1287803250b1e226f0180eb725428d069": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x9cd028b1287803250b1e226f0180eb725428d069"}, "domainSeparator": "0x696d50e3d986b17539d8e15bdf0381e2c716ba65c54cdd0b3d83c3165503bd9b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9cbc2a6ab3f10edf7d71c9cf3b6bdb7ee5629550": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x9cbc2a6ab3f10edf7d71c9cf3b6bdb7ee5629550"}, "domainSeparator": "0xddf9488c4eceffbe9dc9e6c7d2e628b3a7370cc67a530722457ac4b885dd6306", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb1d38026062ac10feda072ca0e9b7e35f1f5795a": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xb1d38026062ac10feda072ca0e9b7e35f1f5795a"}, "domainSeparator": "0xd3c5201d2b135fd1046f6ca44b17972abdfd6e064ced6aa9b5312ffeccf0cdcd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x98c2f9d752e044dc2e1f1743bf0b76a7096eceb2": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x98c2f9d752e044dc2e1f1743bf0b76a7096eceb2"}, "domainSeparator": "0x3e0ada594ed62e05e5f88f43379096287617ca050d62325c02f4cc66d4e4b388", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9d16187d7d8a6adb39d7f373b7e24ea49f97e869": {"kind": "permit", "domain": {"name": "SushiSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x9d16187d7d8a6adb39d7f373b7e24ea49f97e869"}, "domainSeparator": "0x65cd6b68f3c74765f6644ed52c8f86ac9dec10018d8b7f3085c85387c3b92c05", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x16b3e050e9e2f0ac4f1bea1b3e4fdc43d7f062dd": {"kind": "permit", "domain": {"name": "SOMBRA", "version": "1", "chainId": 1, "verifyingContract": "0x16b3e050e9e2f0ac4f1bea1b3e4fdc43d7f062dd"}, "domainSeparator": "0x7542acf483916eccef5b829d41ffe98aeac5e14a88b0783ce9da38dfbba2fe2e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2b8a8845b9bbb8b5beef1d95ef6a60701d867142": {"kind": "permit", "domain": {"name": "Sudo Snacks", "version": "1", "chainId": 1, "verifyingContract": "0x2b8a8845b9bbb8b5beef1d95ef6a60701d867142"}, "domainSeparator": "0xe99f7f8b945f62c933d07e0b70b83653f2683e0b466f9f19cf70a9ca191f3efe", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x93b743fb12a2677adb13093f8ea8464a436da008": {"kind": "permit", "domain": {"name": "Sudo Snacks", "version": "1", "chainId": 1, "verifyingContract": "0x93b743fb12a2677adb13093f8ea8464a436da008"}, "domainSeparator": "0xafad2d5baa8f68994bac277f528af9f7685fd75eb85cecb6927d3d1cc98bb074", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6515e93708a0be2bacda1afcb3b0723f532f2f76": {"kind": "permit", "domain": {"name": "SNKR Coin", "version": "1", "chainId": 1, "verifyingContract": "0x6515e93708a0be2bacda1afcb3b0723f532f2f76"}, "domainSeparator": "0x6cd3e9caa1b723d87c529f48279217d2837948ae6879b8a64262ced6ddc35bfe", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8715ca97c5b464c1957cefbd18015b5567e52060": {"kind": "permit", "domain": {"name": "Snoop DAO", "version": "1", "chainId": 1, "verifyingContract": "0x8715ca97c5b464c1957cefbd18015b5567e52060"}, "domainSeparator": "0x75c1cd4007a34b2bf46b46f605c041c1e461491f949d91268eca7e49fa759902", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6911f552842236bd9e8ea8ddbb3fb414e2c5fa9d": {"kind": "permit", "domain": {"name": "Synapse Network", "version": "1", "chainId": 1, "verifyingContract": "0x6911f552842236bd9e8ea8ddbb3fb414e2c5fa9d"}, "domainSeparator": "0x7050a071dd403ffd45d0899cac6ff7bb8a0282c00fe5f68f5c40b791fc4378d7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x55e45269833cad7060c554279087761a1abc96a1": {"kind": "permit", "domain": {"name": "Sock Aficionado", "version": "1", "chainId": 1, "verifyingContract": "0x55e45269833cad7060c554279087761a1abc96a1"}, "domainSeparator": "0x6ac3bb67f1b8bf723b96ce70a4e54893fcdcac298c16d280a7045615e11b3370", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfe3b138879d6d0555be4132dcfe6e7424e257a2e": {"kind": "permit", "domain": {"name": "SoftDAO", "version": "1", "chainId": 1, "verifyingContract": "0xfe3b138879d6d0555be4132dcfe6e7424e257a2e"}, "domainSeparator": "0x97826242f11a260d5804a5316ac176e1e3c4e76461d5e37fa3f61672c165565a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x04906695d6d12cf5459975d7c3c03356e4ccd460": {"kind": "permit", "domain": {"name": "Staked OHM", "version": "1", "chainId": 1, "verifyingContract": "0x04906695d6d12cf5459975d7c3c03356e4ccd460"}, "domainSeparator": "0x102e3090f685ae938f6bf934921c36e23687c5b6a7dbb3c6e511bdbbf04438cc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x04f2694c8fcee23e8fd0dfea1d4f5bb8c352111f": {"kind": "permit", "domain": {"name": "Staked Olympus", "version": "1", "chainId": 1, "verifyingContract": "0x04f2694c8fcee23e8fd0dfea1d4f5bb8c352111f"}, "domainSeparator": "0xb0bf5e43681b0a9e5f7dc6a4dbcf80aed4a3b05bc067b63534cfb8c78a8ace44", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf55e77c77f933d252fbb6692f4cc060ad82de510": {"kind": "permit", "domain": {"name": "ShibOHM", "version": "1", "chainId": 1, "verifyingContract": "0xf55e77c77f933d252fbb6692f4cc060ad82de510"}, "domainSeparator": "0xdb23cae6e3942b9db10944d6499079b0e6d2ee2afd3f7b8e1ce6fdb4878fa585", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x501ace9c35e60f03a2af4d484f49f9b1efde9f40": {"kind": "permit", "domain": {"name": "solace", "version": "1", "chainId": 1, "verifyingContract": "0x501ace9c35e60f03a2af4d484f49f9b1efde9f40"}, "domainSeparator": "0x4a34687b2fd531c493860ded45f3d837673bb85b3aec44700d93a2eeedbda7ff", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3dc7b06dd0b1f08ef9acbbd2564f8605b4868eea": {"kind": "permit", "domain": {"name": "Space Token", "version": "1", "chainId": 1, "verifyingContract": "0x3dc7b06dd0b1f08ef9acbbd2564f8605b4868eea"}, "domainSeparator": "0xe699a0f84375f6c2420964edbef3dc37faaa723a514f4c3d665727f5e3021499", "domainSeparatorFn": "domainSeparator()", "nonce": "nonces()"}, "0x090185f2135308bad17527004364ebcc2d37e5f6": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x090185f2135308bad17527004364ebcc2d37e5f6"}, "domainSeparator": "0x4ec28097760d69e8a4c400eba86dcf8ed01cc5eb294f03b7f5125d6384b84c96", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x99bb54f7a11eb8af1d486fe644ebed304f52f85a": {"kind": "permit", "domain": {"name": "SpongeBob Square", "version": "1", "chainId": 1, "verifyingContract": "0x99bb54f7a11eb8af1d486fe644ebed304f52f85a"}, "domainSeparator": "0x6e08af357f5bd01d6ce7d3930b46f2df4716c840d739e279b41742064bd31953", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x54005bec43d8ffd2ec92edeceff7d952693294cd": {"kind": "permit", "domain": {"name": "Spaceships Coin", "version": "1", "chainId": 1, "verifyingContract": "0x54005bec43d8ffd2ec92edeceff7d952693294cd"}, "domainSeparator": "0x826558154ad6da7c490d6265393c1a7badd7252002980843c4206eafe4bc1d54", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x21ad647b8f4fe333212e735bfc1f36b4941e6ad2": {"kind": "permit", "domain": {"name": "Squid", "version": "1", "chainId": 1, "verifyingContract": "0x21ad647b8f4fe333212e735bfc1f36b4941e6ad2"}, "domainSeparator": "0x71b2da752ce9b47e5dc6e6a054a6fed5a20d4cae185a29d98e6778b5b7283142", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcf6daab95c476106eca715d48de4b13287ffdeaa": {"kind": "permit", "domain": {"name": "ShibaSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xcf6daab95c476106eca715d48de4b13287ffdeaa"}, "domainSeparator": "0xda0391551db4dac774b550d667b56f72b4f2424a15b3f6fcc8653aaa2acf6b49", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xefb47fcfcad4f96c83d4ca676842fb03ef20a477": {"kind": "permit", "domain": {"name": "ShibaSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0xefb47fcfcad4f96c83d4ca676842fb03ef20a477"}, "domainSeparator": "0x64228d1f20cbb6bb4f80fcae5c85b473e65d76ca5ac828302b23e6fe6368a1c6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x57654ae132413e81459ad2ae70c2570a9b89fb53": {"kind": "permit", "domain": {"name": "ShibaSwap LP Token", "version": "1", "chainId": 1, "verifyingContract": "0x57654ae132413e81459ad2ae70c2570a9b89fb53"}, "domainSeparator": "0x47794634533ab882810752017cd9fa3837d2d429b3b25d01bfc9aa51adfe00ae", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x26fa3fffb6efe8c1e69103acb4044c26b9a106a9": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x26fa3fffb6efe8c1e69103acb4044c26b9a106a9"}, "domainSeparator": "0x88991a7f2933bac43f770e43c4d4de2d56b73f8f84473edcc8fda39b142082c2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0ae055097c6d159879521c384f1d2123d1f195e6": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "STAKE", "version": "1", "chainId": 1, "verifyingContract": "0x0ae055097c6d159879521c384f1d2123d1f195e6"}, "domainSeparator": "0xfb091569f725c7758bd968c1c7747915121b9d4e37a12097a0f4bdc2e52b0ab3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1d7ca62f6af49ec66f6680b8606e634e55ef22c1": {"kind": "permit", "domain": {"name": "BSCstarter", "version": "1", "chainId": 1, "verifyingContract": "0x1d7ca62f6af49ec66f6680b8606e634e55ef22c1"}, "domainSeparator": "0xbfaac72d242929c6dfac853e2f020ba96f4e9cc00d9b7eb276006a1991f0265d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "New World Order", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x85052bf5516cb8b01077a684829ff166e83a9d93750fc9e4a05c633bb3b21ec3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Staked ETH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x953f5f7573e729e7f77a653cdf2994c27ab4e15d144a9cc1b6ed4cbabf267eff", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "StarkNet Token", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xba91cbe2d0dcf0b7df9b3bf179745c516ba16825f1b617f3ef1181c1dfca5d30", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Instrumental Finance", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x2fd88b627e32b0acfb8dbffd6859fa5ece6e3a748badfc694947c5fca64f8f0b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x90b426067be0b0ff5de257bc4dd6a4815ea03b5f": {"kind": "permit", "domain": {"name": "Strain", "chainId": 1, "verifyingContract": "0x90b426067be0b0ff5de257bc4dd6a4815ea03b5f"}, "domainSeparator": "0x4ffa8e51ec0b492f7946720eb93c584fb101f2e497071b7173abbb83ba463eb6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdc0327d50e6c73db2f8117760592c8bbf1cdcf38": {"kind": "permit", "domain": {"name": "Stronger", "version": "1", "chainId": 1, "verifyingContract": "0xdc0327d50e6c73db2f8117760592c8bbf1cdcf38"}, "domainSeparator": "0x24f8802dc3446f65ee38f502bb88211859f808d79505c6718543f9aecc03026c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0ba0d270f4594099877b6096e310b77396664039": {"kind": "permit", "domain": {"name": "Suzume2.0", "version": "1", "chainId": 1, "verifyingContract": "0x0ba0d270f4594099877b6096e310b77396664039"}, "domainSeparator": "0x09f0dee081714d39f014f2aa539af46cc76c1ef5f05b043ed04682cf426f9104", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3446dd70b2d52a6bf4a5a192d9b0a161295ab7f9": {"kind": "permit", "domain": {"name": "SUDO GOVERNANCE TOKEN", "version": "1", "chainId": 1, "verifyingContract": "0x3446dd70b2d52a6bf4a5a192d9b0a161295ab7f9"}, "domainSeparator": "0xb3b3022f55bdf5ca27f79d96e87b05e17bc730a7cf03af502e0e27e4d9dd19c1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8d7cfe0c1045ed9d3e65f9746f270c9c1f65262a": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x8d7cfe0c1045ed9d3e65f9746f270c9c1f65262a"}, "domainSeparator": "0xc65fe116e7daad8c50e4d8726f4caf07b2b2d018c5c6f895cfc23a912ff06e09", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x691d7f624e91201ca3e8b1ea2336c583d643ca57": {"kind": "permit", "domain": {"name": "SuperBid", "version": "1", "chainId": 1, "verifyingContract": "0x691d7f624e91201ca3e8b1ea2336c583d643ca57"}, "domainSeparator": "0x00af7d079841c899da643fa4f18303d0e00f27c3fb73fce87ce34590aee31ce0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x314fdc725ba9028af758f8a23270d84ebbd1e9e6": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Stable USD", "version": "1", "chainId": 1, "verifyingContract": "0x314fdc725ba9028af758f8a23270d84ebbd1e9e6"}, "domainSeparator": "0x14620e8154e041935ef4af4f6dcf24c93219f9a006c461d8e79d9a12b1155cfc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x57db3ffca78dbbe0efa0ec745d55f62aa0cbd345": {"kind": "permit", "domain": {"name": "Symmetric", "version": "1", "chainId": 1, "verifyingContract": "0x57db3ffca78dbbe0efa0ec745d55f62aa0cbd345"}, "domainSeparator": "0xad772914783fb096b846fe053d790d4face8409af9b10830b81af640d6942cd3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x86389efb27908a8724444b17f5056767a9a15d1a": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "Take", "version": "2", "chainId": 1, "verifyingContract": "0x86389efb27908a8724444b17f5056767a9a15d1a"}, "domainSeparator": "0x0aa8581ad73e513eb0ad6796d0ab73693e56cd202299dd248a7c7ffec59c4c35", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x549ea7d6b3cc50ecd57ce2c6e4e36329d26e964d": {"kind": "permit", "domain": {"name": "Tankbottoms", "version": "1", "chainId": 1, "verifyingContract": "0x549ea7d6b3cc50ecd57ce2c6e4e36329d26e964d"}, "domainSeparator": "0x1ad470b0ef2ed962e8410329374de80a69c44c4a8727c42cbce96f52ad25f019", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x08a647fbe57c846e3b9ce9caa5689d305e5f5259": {"kind": "permit", "domain": {"name": "Test Aquasis token", "version": "1", "chainId": 1, "verifyingContract": "0x08a647fbe57c846e3b9ce9caa5689d305e5f5259"}, "domainSeparator": "0x79f2075b43414713579fc7393dc00a38fdf58f006554218ad544a362999d7a8d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x890f6dc7f57ac3d5b354d1331e8eff463bb2e034": {"kind": "permit", "domain": {"name": "Tate<PERSON>o<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x890f6dc7f57ac3d5b354d1331e8eff463bb2e034"}, "domainSeparator": "0x5fb89c652cc3637331a783cae2dd42d9cf182f6123edb5f660e977fae3b104e8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "TeraBlock Token", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x5e84f4940eb489a908407a22fc51dc3a5529098d730b105b5ff6087503c54fa1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "tBTC v2", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x85df006d8efff5040f4fdd173197325caa418545ff9f6fbab526b624756ff362", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "TecraCoin", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x4d803abeb2b7d24aa2a6864e7c7c11ce2d9a7f2a42f0f284c17c2c9fc5f6d624", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Timechain Swap <PERSON> ", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xf482313f081e8d50bb601df36ccb31704064a437b80050a080cfd9bdb350e9f9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x89d71dfbdd6ebecd0dfe27d55189f903169d2991": {"kind": "permit", "domain": {"name": "THDX", "version": "2", "chainId": 1, "verifyingContract": "0x89d71dfbdd6ebecd0dfe27d55189f903169d2991"}, "domainSeparator": "0x4620966de60d0998848dd6b49b60ea3dfcb2c756ec629fca7e38ee31c62a0ff5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5437c65d9964474c5bbae7335ce72a5f9373d782": {"kind": "permit", "domain": {"name": "The Protocol Reloaded", "version": "1", "chainId": 1, "verifyingContract": "0x5437c65d9964474c5bbae7335ce72a5f9373d782"}, "domainSeparator": "0x0cb209e507510efc91641aa68b8e36aa1fe75a8fd1161d6526dcf4dedc3c98a9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb444c9f907254b60cb1e7637baea86f5558c4434": {"kind": "permit", "domain": {"name": "The Dev", "version": "1", "chainId": 1, "verifyingContract": "0xb444c9f907254b60cb1e7637baea86f5558c4434"}, "domainSeparator": "0xadcd5c06ed212409995e72e610145400bb4698dc2f0d95f5d0741684ee8ba168", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x82f77e7497826fb4578ad5f60575154d880f3790": {"kind": "permit", "domain": {"name": "<PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x82f77e7497826fb4578ad5f60575154d880f3790"}, "domainSeparator": "0x22213b53aa26efdca8d25c51c65345c4fab9097191ac0e21e6343682b89b1ddd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbbf81c5e7d0dc140e26472128d3b4629001c6de6": {"kind": "permit", "domain": {"name": "THE Inu", "version": "1", "chainId": 1, "verifyingContract": "0xbbf81c5e7d0dc140e26472128d3b4629001c6de6"}, "domainSeparator": "0xf245408fc4e677b21f8def6d551177bd107613545b09938db9c74ab0df8a8ce1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf4781b33f2aa5977ccecaa53519c06571985116b": {"kind": "permit", "domain": {"name": "Tsinghua University Token", "version": "1", "chainId": 1, "verifyingContract": "0xf4781b33f2aa5977ccecaa53519c06571985116b"}, "domainSeparator": "0x64f9f39294f896da1b1ef5401cdd8ba8f3b7918bbdc04c7b8f4936845c153cbb", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1d6ea8f676d1da91022fcc185f19ce9665109696": {"kind": "permit", "domain": {"name": "TrustMoon", "version": "1", "chainId": 1, "verifyingContract": "0x1d6ea8f676d1da91022fcc185f19ce9665109696"}, "domainSeparator": "0xb2c40afd3e213b4133cebe356147775c5bea48205e44cf20e69f55627dc0115f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xaee433adebe0fbb88daa47ef0c1a513caa52ef02": {"kind": "permit", "domain": {"name": "PontoonToken", "version": "1", "chainId": 1, "verifyingContract": "0xaee433adebe0fbb88daa47ef0c1a513caa52ef02"}, "domainSeparator": "0xa94718cebdc3ba8364f40772611ca7ed0b33edcabd70bce496d0b0ef42abafb8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x409c4d8cd5d2924b9bc5509230d16a61289c8153": {"kind": "permit", "domain": {"name": "TONStarter", "version": "1.0", "chainId": 1, "verifyingContract": "0x409c4d8cd5d2924b9bc5509230d16a61289c8153"}, "domainSeparator": "0xa89986a6d1d3246e39c134bb8b7d47f7ecd8472e648ff543c0f560b59452b760", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6e5970dbd6fc7eb1f29c6d2edf2bc4c36124c0c1": {"kind": "permit", "domain": {"name": "Polytrade", "version": "1", "chainId": 1, "verifyingContract": "0x6e5970dbd6fc7eb1f29c6d2edf2bc4c36124c0c1"}, "domainSeparator": "0xea0b1918fb7cde63566ebbff920eaf6229d71aa6d13e1bfc957856fdecd34913", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe53abf2d3bce282a145cc4381ee16b1ff8059da6": {"kind": "permit", "domain": {"name": "MetaTreeDAO", "version": "1", "chainId": 1, "verifyingContract": "0xe53abf2d3bce282a145cc4381ee16b1ff8059da6"}, "domainSeparator": "0x3837309cc033a9a9387623abfebc8b878710cf1574891095946ea3ed40ab62a4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x93eeb426782bd88fcd4b48d7b0368cf061044928": {"kind": "permit", "domain": {"name": "The Rug Game", "version": "1", "chainId": 1, "verifyingContract": "0x93eeb426782bd88fcd4b48d7b0368cf061044928"}, "domainSeparator": "0xd7b892dec479048dbbd0b7678771f1c581031f08f29925d3740a8e682fd76f75", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x30e74b8ffde15099bee8e7d4abe20d0440f9c3c9": {"kind": "permit", "domain": {"name": "Tripamine", "version": "1", "chainId": 1, "verifyingContract": "0x30e74b8ffde15099bee8e7d4abe20d0440f9c3c9"}, "domainSeparator": "0xf73132f34a091d4c2e30cea9a80b863003124eb9329e9ec8ee9111f612b42eb2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfc13d141b5b11bbd27f6b5f88304cf1f63b6f992": {"kind": "permit", "domain": {"name": "TateWhales", "version": "1", "chainId": 1, "verifyingContract": "0xfc13d141b5b11bbd27f6b5f88304cf1f63b6f992"}, "domainSeparator": "0x6c511da5f4591db667893146d002f301a2275e513c423ccd10adfb345542f021", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x097b9959bfcf715513ecd30c4b1fb469ef32b743": {"kind": "permit", "domain": {"name": "The Yellow Blocks", "version": "1", "chainId": 1, "verifyingContract": "0x097b9959bfcf715513ecd30c4b1fb469ef32b743"}, "domainSeparator": "0x99b589182a1e6df3a27103da519fd8b3f1ea6b52b2432f40dba140942005c0ad", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0f644658510c95cb46955e55d7ba9dda9e9fbec6": {"kind": "permit", "domain": {"name": "Ubiquity Algorithmic Dollar", "version": "1", "chainId": 1, "verifyingContract": "0x0f644658510c95cb46955e55d7ba9dda9e9fbec6"}, "domainSeparator": "0x414a94f668f74b66238ab37f58a74be336f97f86f3c6ecf3ab70932ba4d08e55", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4e38d89362f7e5db0096ce44ebd021c3962aa9a0": {"kind": "permit", "domain": {"name": "Ubiquity", "version": "1", "chainId": 1, "verifyingContract": "0x4e38d89362f7e5db0096ce44ebd021c3962aa9a0"}, "domainSeparator": "0xe082a001063b53e35cdfbeae5f60481978e3ee17d674da35a80e7be162d09a5f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb1db366890eeb8f28c2813c6a6084353e0b90713": {"kind": "permit", "domain": {"name": "UniCandy", "version": "1", "chainId": 1, "verifyingContract": "0xb1db366890eeb8f28c2813c6a6084353e0b90713"}, "domainSeparator": "0xf32868e0cfed825bca3063ef855303592be8f279e929680e0d37ba050089bd21", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x35bcf2b1c21562579c3d88ceb9d7149c96397545": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x35bcf2b1c21562579c3d88ceb9d7149c96397545"}, "domainSeparator": "0xdc190907659639ff724a246d7cb5a789da53d556a2e1d126bc9b3fb0ab8d4c0c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "UERII", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0f802017da94f5ea4d47e316a72224b3fbe9d352e5c483a0d03f582d37180ad3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "microETH", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xe4b0a1e108ecf621178330a27f31323c8b5a3b4898bd7bcfba6f8dcb5ccb2c5d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "UnicornDAO", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x8fd51210f8a9179228db6299a92d3ff8b7bbaedd6bda78790eac5569cb59755f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x8cfe016abdf5f54b6d294a7c542b755877b8129a5c203625cc0085c94795f2a3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe2aab7232a9545f29112f9e6441661fd6eeb0a5d": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xe2aab7232a9545f29112f9e6441661fd6eeb0a5d"}, "domainSeparator": "0x7bd150272503bbc8264c835f7c64477721bed5d006866aa9b6d5f0df78f5ecce", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe7b957d982e38112e1eac74fc5e5889619b2d177": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xe7b957d982e38112e1eac74fc5e5889619b2d177"}, "domainSeparator": "0xe5632433045d3a6f98b1c0a6f90a10c719227df55de6f36d5d8d1daff4967f61", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdfa42ba0130425b21a1568507b084cc246fb0c8f": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xdfa42ba0130425b21a1568507b084cc246fb0c8f"}, "domainSeparator": "0x46dd58f6e6ece14a04b97dd09501e51d9151443cb86b3b36cb85b14e9646d38d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe5d6fc7323d74ad440ca53d3e53252427ad632ed": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xe5d6fc7323d74ad440ca53d3e53252427ad632ed"}, "domainSeparator": "0xfa39a3b3081d2afee1c606b3bda8b9c217f43e78672fc2608e94f93c238a710d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdc98556ce24f007a5ef6dc1ce96322d65832a819": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xdc98556ce24f007a5ef6dc1ce96322d65832a819"}, "domainSeparator": "0xfb8cd53e94e55b170e179b2fb2cebb09dc10aab44a4fea70a8e6bb5cb1893aa1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe1573b9d29e2183b1af0e743dc2754979a40d237": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xe1573b9d29e2183b1af0e743dc2754979a40d237"}, "domainSeparator": "0x87e72d224ef5528c4020b1951aec2227b32d90689a477594a7b14cd06a1b8497", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdfc14d2af169b0d36c4eff567ada9b2e0cae044f": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xdfc14d2af169b0d36c4eff567ada9b2e0cae044f"}, "domainSeparator": "0x360404c620d5b15ee1a03cb4e9f05d590792c37ed3507630d41f92379543c598", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdeb706fc00e335092532587f5b8c3829ebdab303": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xdeb706fc00e335092532587f5b8c3829ebdab303"}, "domainSeparator": "0xb94f5631d12ac98189cbda032f6ebd7df94b6ac60e7358ce2511023c61356737", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xedaedd22e653c504ff6806bf61664292848eb26e": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xedaedd22e653c504ff6806bf61664292848eb26e"}, "domainSeparator": "0x710cb8f7d1c190a443a868cda93cbc8fb2764081d49c163522e7e4fe25bccf69", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xdd6fba84245f5ab20a8e39ce7303b243eb52aad1": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xdd6fba84245f5ab20a8e39ce7303b243eb52aad1"}, "domainSeparator": "0xd1c4f5addf86210927ac64e197a54f2056156eb4eb0e5b53f095deaa3eb06e8c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfac2c93fb88a1536578e1a44936191d24effc681": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xfac2c93fb88a1536578e1a44936191d24effc681"}, "domainSeparator": "0xbf3b65db1129e012ad907b88698ec8df9ed70f6e9dbeb03f3cd0cec93b1d37d5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfb51a66e5684f2ed6027cc5816bcf7b720396f23": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xfb51a66e5684f2ed6027cc5816bcf7b720396f23"}, "domainSeparator": "0x3ad9234f5752b871e5ceda931387be97fead35269ba6c16bb7e4c5acdecca6d2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xfa29c55326c81c79b81f190f0439bae108604804": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xfa29c55326c81c79b81f190f0439bae108604804"}, "domainSeparator": "0x4631446af39ba47016c7f91feaf6ae7aeac5002019b1be40e1096b690fcd3b3d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe58eb0e623d06e329020813568124e3541a36fcc": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xe58eb0e623d06e329020813568124e3541a36fcc"}, "domainSeparator": "0x73259220d707b24330335a1747ed68599b616bd16c81811026e0c7e7816af63d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf047ee812b21050186f86106f6cabdfec35366c6": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xf047ee812b21050186f86106f6cabdfec35366c6"}, "domainSeparator": "0x58e918beb6a1cf12b991b7ce6e4d778804fcbacebb0268fcbc36be5ef2eef03e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb1a6876ad98fba649a66be916559039abdd3dbbe": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb1a6876ad98fba649a66be916559039abdd3dbbe"}, "domainSeparator": "0x6a6ec17ee20ff5977ce824e59e0af490addc62528ba9bd1d1adda62b0c835d9c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9507568a74b0e43456b902adea648d3f6954f570": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x9507568a74b0e43456b902adea648d3f6954f570"}, "domainSeparator": "0x5faa2e3d0a2595ef4a06ce5f00474200a6dc20cfbe11a16f6cf5bfb7986d6f30", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa478c2975ab1ea89e8196811f51a7b7ade33eb11": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xa478c2975ab1ea89e8196811f51a7b7ade33eb11"}, "domainSeparator": "0x2e6f67df6ceec45c079369ce4cfc069e306519f776d90b4e1f6d30ff9d94f8e3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9ec5149472db6acffb9023a47d37b4ecbcf68a4b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x9ec5149472db6acffb9023a47d37b4ecbcf68a4b"}, "domainSeparator": "0x301342ae423c9bcf249d5c103bc1ab7193c201b2c391da0257e9755fbf9f4133", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x97be09f2523b39b835da9ea3857cfa1d3c660cbb": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x97be09f2523b39b835da9ea3857cfa1d3c660cbb"}, "domainSeparator": "0xedb7f5928bbeea10b96e40587a1112e52d2397be7377d5792704037d7ca333ea", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb469899812f74ee43bffe2d2022590111da86425": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb469899812f74ee43bffe2d2022590111da86425"}, "domainSeparator": "0x54c827d78729177a946829e5735926e5a9012940f78d51497a9a789801ec024b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa938b18d435f7b217ffcb810de05a095a119edbc": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xa938b18d435f7b217ffcb810de05a095a119edbc"}, "domainSeparator": "0xd49599c835a60d68becb7b513eabb5b5ce608c949d1867ebc74ed6bd020146ed", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x93e7dec988e24b08cc1606061b232deeb80ffbf8": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x93e7dec988e24b08cc1606061b232deeb80ffbf8"}, "domainSeparator": "0xd29f1918b03e39d074135ab0988bd8dac5150cb006fdd8646a667e18186e2276", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa00d47b4b304792eb07b09233467b690db847c91": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xa00d47b4b304792eb07b09233467b690db847c91"}, "domainSeparator": "0xca0b738439bff3749c0133b1c89a44fd1bbf2663efae2b29f040403450865e49", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xab3b03808b67c45c8103e7e1f010cab82ab9a614": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xab3b03808b67c45c8103e7e1f010cab82ab9a614"}, "domainSeparator": "0xc3f708cff3adfcc50260eea6ade7e305d0eb5138cb90f982aba7aaba89209d11", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb1b537b7272ba1eda0086e2f480adca72c0b511c": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb1b537b7272ba1eda0086e2f480adca72c0b511c"}, "domainSeparator": "0x547199717bd7bc6e3eda33652135ab3075e4918ba98bf10505b9311b8083f54c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xaad22f5543fcdaa694b68f94be177b561836ae57": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xaad22f5543fcdaa694b68f94be177b561836ae57"}, "domainSeparator": "0x3d8d56c9b2c2352d4098d7df939031bf0c436317f2365df1341147640e9a3501", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x944fd50276599807625786fb8edc9876c54565e8": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x944fd50276599807625786fb8edc9876c54565e8"}, "domainSeparator": "0x4295d1e4799fbda60faa543d72b4a8292dab6f6d4d012f15478657fc583cdf0a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x919b599ecb6c9a474a046d1252b2f41f8047decb": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x919b599ecb6c9a474a046d1252b2f41f8047decb"}, "domainSeparator": "0x162b7bb77e2889fcc399b48a9fff2acb8eac87c916ab1001404975a217ca93ed", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb13201b48b1e61593df055576964d0b3aab66ea3": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb13201b48b1e61593df055576964d0b3aab66ea3"}, "domainSeparator": "0x8b61d8adf1f3d304263b07234545f147e584c50be079ce73843769a3b1f267d2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa23c4aa7050425d2922956bedd9d513da1b4a977": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xa23c4aa7050425d2922956bedd9d513da1b4a977"}, "domainSeparator": "0x18d13ab31195c89d64e6e31551c8a4dc093f68c333ef629318f6295098e82ce0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9861f7187b58023d89b2c2b0767bdee43345620a": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x9861f7187b58023d89b2c2b0767bdee43345620a"}, "domainSeparator": "0x3369089718498e8dab1e43192d528702d77e9225f9b7fd69802277ddb54e52c3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x9b7dad79fc16106b47a3dab791f389c167e15eb0": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x9b7dad79fc16106b47a3dab791f389c167e15eb0"}, "domainSeparator": "0x7579424b9ee285703f3e3a4a6105d78e47376ac554829fdb4f19d936ebef2d36", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa14c40b83885e1f8d516e0e43a7361f31eca6854": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xa14c40b83885e1f8d516e0e43a7361f31eca6854"}, "domainSeparator": "0xe88aa5850618444213ae4b2c66494497785fd31fd0326870041b0b82f3a66c49", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xa5ab281f4e794b80cc8155103f55f2522e83999d": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xa5ab281f4e794b80cc8155103f55f2522e83999d"}, "domainSeparator": "0x6b39c4109936f9f51a2111025f2805116016716b2a9077ea4231522217d3b90b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x78b668acc8233daf8780d0d9260c1092a3507e01": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x78b668acc8233daf8780d0d9260c1092a3507e01"}, "domainSeparator": "0xe45091aae873efa5589b37bf9f5f1a6ceab350326b9935087f42eeb4802e71f2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8804fcdd7d77d2f9007399e819b7524addfd00a9": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x8804fcdd7d77d2f9007399e819b7524addfd00a9"}, "domainSeparator": "0xfa24e3d3c8f021627bf7af8e26bfd81df8a74aeaad2830b0e91a974448364ef7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x769af5167a6e060be22d7187e16e11304b512dda": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x769af5167a6e060be22d7187e16e11304b512dda"}, "domainSeparator": "0x6dcb52087ad95df1b8879d2bc688dc77a254e934cee6918b1f5f678bcfee199c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x735152dc18fd8ce2b93ef3a8de3a978d6a38df5b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x735152dc18fd8ce2b93ef3a8de3a978d6a38df5b"}, "domainSeparator": "0xe9045b85a6e0b78e036b298eaa3c9126a91c5cf8f9cdff0e17546448cf408e96", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x82d6b30bff73c6363d024cbc2f44d1cbbee5972e": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x82d6b30bff73c6363d024cbc2f44d1cbbee5972e"}, "domainSeparator": "0x1eb45a56826bd5c272388263a2320075bdc62ae7e8791c32048cbdf8ef7809a8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x86fef14c27c78deaeb4349fd959caa11fc5b5d75": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x86fef14c27c78deaeb4349fd959caa11fc5b5d75"}, "domainSeparator": "0xc0ea818c0b538e2017c8c58dc49b54341dee0ae1483008081634fa603e3693ec", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x87febfb3ac5791034fd5ef1a615e9d9627c2665d": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x87febfb3ac5791034fd5ef1a615e9d9627c2665d"}, "domainSeparator": "0x91a4d54029c9079a670a0b7c6bf885ecf64220605060257cdef0fd7e6d07febd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6fad7d44640c5cd0120deec0301e8cf850becb68": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x6fad7d44640c5cd0120deec0301e8cf850becb68"}, "domainSeparator": "0xcadad0f5c466d52875dff5bc9a350cc0c8ac776438d1d4d54692c6f3e3d0f3a8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8e6083970f9ce8f0594ede2bbe39edf0ad201dd1": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x8e6083970f9ce8f0594ede2bbe39edf0ad201dd1"}, "domainSeparator": "0xa0e4b628ef48a632b87cf6907bd0456eda61745caeb660f6a438bbaf9e13b6c2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x76cc78cc8923361908003d627b285dc63e6069ca": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x76cc78cc8923361908003d627b285dc63e6069ca"}, "domainSeparator": "0x16d4bce696379cc7b0118d8e8fee7792868fcc7f5fceb6bdb223e1dd899ae518", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8cab3e311702acb64e250926d77134fde604bd4d": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x8cab3e311702acb64e250926d77134fde604bd4d"}, "domainSeparator": "0xb1932adba884470d7ea041ceac1d63d80e3afd217b189079ab5fc8e0aabf6785", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x901380ab7b9e8a0ecfb14bd5219e4a7d762c656d": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x901380ab7b9e8a0ecfb14bd5219e4a7d762c656d"}, "domainSeparator": "0xe997bcf7f902e73ba68e91456d8b9cbf252b6cf23a763274ab1324ec53ff6af2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7361089739160c3fe987a06bf50283487b671219": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x7361089739160c3fe987a06bf50283487b671219"}, "domainSeparator": "0x376e38677ab8981cae9949d21a28e3572c7c74bb51fe978ed50c3f5119640a3c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xca9aa40d435d7388618ff9ca8c98c9b5566f7a23": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xca9aa40d435d7388618ff9ca8c98c9b5566f7a23"}, "domainSeparator": "0xc66e2b8f4c6acfc7b314241d6443b1eebf4169bece71cbb5fc392a18e73cdfca", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbb2b8038a1640196fbe3e38816f3e67cba72d940": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xbb2b8038a1640196fbe3e38816f3e67cba72d940"}, "domainSeparator": "0x9b77362d4dc4f688a1b9b270a02a1589a8059e519ba88d29d2a63d3738df3d0e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd5fb669db8f85c2be391adeffe7e8c781995795b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xd5fb669db8f85c2be391adeffe7e8c781995795b"}, "domainSeparator": "0x2fc57e62df491ae52d9d3720162fc966fef9819d9930bcad67a5c0f675104c8f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb5b638706cf6cac54c71e39ab00982b6317de908": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb5b638706cf6cac54c71e39ab00982b6317de908"}, "domainSeparator": "0x5655ab3bf36a42dc77f489b35e2b9f11c1998c856a79bee20ef5ddc8dc15ef4b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc5be99a02c6857f9eac67bbce58df5572498f40c": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xc5be99a02c6857f9eac67bbce58df5572498f40c"}, "domainSeparator": "0x60d0f818668cbf118e5eded4839514f79b8582ca62312248a4aac4196cbb055d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xce84867c3c02b05dc570d0135103d3fb9cc19433": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xce84867c3c02b05dc570d0135103d3fb9cc19433"}, "domainSeparator": "0x28ae290f8eecbe56b18a41d8fbb2a2208c00d6a9d0187ea2f978ba77cae9acab", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb96a7b515dacb0f359515e7fb08babf2651db3c0": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb96a7b515dacb0f359515e7fb08babf2651db3c0"}, "domainSeparator": "0xa88f2b1123a8cee1aabe396e5cee0049c5a437b8a6a11dd85259c69cfb5cf7d3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc72a90093839bccfae757db12d4f15ac5cab2f09": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xc72a90093839bccfae757db12d4f15ac5cab2f09"}, "domainSeparator": "0xb2e22aa09db57441a9549a7a626f536f7d15bdb0fd2ef864bad9bfeb17b148d6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc759c0e2b6bcb8504c70b8045b3e8990852fac2a": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xc759c0e2b6bcb8504c70b8045b3e8990852fac2a"}, "domainSeparator": "0xef0df72e1bf17f0428e7b2682f5aafe9d7395ebd171d6c18525cb7c850fdf927", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb93cc05334093c6b3b8bfd29933bb8d5c031cabc": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb93cc05334093c6b3b8bfd29933bb8d5c031cabc"}, "domainSeparator": "0x95e7c82d15e2441c357f622db5388092e8c3040a7388babaa944f00ab6dd29ed", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbb0444980898acc79de3b66a7025f24fc720f2e5": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xbb0444980898acc79de3b66a7025f24fc720f2e5"}, "domainSeparator": "0xaef3ab22fb45006603cf737513c8a05ebcbc43692bf1c74e94bcc69a56a3de07", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc7a729d6d010ffdea743092a97898e450ec1ed33": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xc7a729d6d010ffdea743092a97898e450ec1ed33"}, "domainSeparator": "0x065b49cf33f4a558d244732b575449ef4b016db26cb7710be0d57ecc313234e9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd5781d21d3a7c871e4ded41542002295c9408f8c": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xd5781d21d3a7c871e4ded41542002295c9408f8c"}, "domainSeparator": "0x2551937744ff2ced41e9078a26fa50412e9856b0892bb0e0294e058d11ae2c58", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xc822d85d2dcedfaf2cefcf69dbd5588e7ffc9f10": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xc822d85d2dcedfaf2cefcf69dbd5588e7ffc9f10"}, "domainSeparator": "0xc06440ee76355587f093274797dbbf0641721b6ef8205ad1a166ddc1e35f3e52", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbacd2cd0c094c607902f10390187b31e6845bca0": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xbacd2cd0c094c607902f10390187b31e6845bca0"}, "domainSeparator": "0x9aa5b0a2f38303a5847e775675836602077fd9af3c5030ad94b296f44041a2e3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba08c283487849fe5c14d1d1e5f93f84092819f0": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xba08c283487849fe5c14d1d1e5f93f84092819f0"}, "domainSeparator": "0x7d4c7a2b698d6242ad684fb01592895af3e01d50cb2707e0a8ac3a7688e8ef1a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb64f549c13975732a37eb252ec128f5b0872f5a8": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xb64f549c13975732a37eb252ec128f5b0872f5a8"}, "domainSeparator": "0x177fc5b631ef8bf1d739701a4c8405b843a907a124ffdd67d12ff3c342a2b2fe", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcd3c5d24831877df1bf80e1cebac2f5d9ac530b0": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xcd3c5d24831877df1bf80e1cebac2f5d9ac530b0"}, "domainSeparator": "0xfcbe8f5725e562130f4f7018138a85e60ebe16a15470008d646682f304b626b8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xd3d2e2692501a5c9ca623199d38826e513033a17": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xd3d2e2692501a5c9ca623199d38826e513033a17"}, "domainSeparator": "0xca369e92bd4244f13141578afe534b58316c10dc8ba7d17f74703d8943772844", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x18a5c094d811e1ef9116fa0bbc5cd23a70cfcddd": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x18a5c094d811e1ef9116fa0bbc5cd23a70cfcddd"}, "domainSeparator": "0x4840bd066e93f4c1850fcaedc450461fd09a54e287fe9205b7fdcdd5dd92843f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2084c8115d97a12114a70a27198c3591b6df7d3e": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x2084c8115d97a12114a70a27198c3591b6df7d3e"}, "domainSeparator": "0x66fc127a571910dc6ea02e95560947b044ca8f604f02dd4ac91cea51df6a6f3c", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1d4b2b2a2ca8762410801b51f128b73743439e39": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x1d4b2b2a2ca8762410801b51f128b73743439e39"}, "domainSeparator": "0x78835539f4915813822139bd952fcc11bd88a1b73f6e86768e6dbdead8080151", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0df411cd73dd664237a8e67d43c3e13d6e7d2bff": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x0df411cd73dd664237a8e67d43c3e13d6e7d2bff"}, "domainSeparator": "0x8ff37ce2281fb20b9e87f3d18ee871ff7dfb9d3db4283e8357ab8cb52aea3e10", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1d177da406e3c1419e3bc07b16794f58f6bbd421": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x1d177da406e3c1419e3bc07b16794f58f6bbd421"}, "domainSeparator": "0x0b56dafb15197d6dfb7dd7673ea749e177d5a830c7f9477a1b3855d1d2ae4dce", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x22508de3dc071801fa553579d7110875ac0e3052": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x22508de3dc071801fa553579d7110875ac0e3052"}, "domainSeparator": "0x4949da0d3459d41f8e5372bc4c44a502c883dd8f4a1a9703475c114e4e5432d0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1c48b1def1404750359d3ff5a765a90c3f702999": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x1c48b1def1404750359d3ff5a765a90c3f702999"}, "domainSeparator": "0x2b7eaf4ea866081d82740ea6e46eef4e17282cc7f66b7ff866017ccac88fda8f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0d4a11d5eeaac28ec3f61d100daf4d40471f1852": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x0d4a11d5eeaac28ec3f61d100daf4d40471f1852"}, "domainSeparator": "0x69c3ee28fa3d92cf21f81e15ae19cda0075ea3221c108e59a8e9d9ed4e34625e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x01f8989c1e556f5c89c7d46786db98eeaae82c33": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x01f8989c1e556f5c89c7d46786db98eeaae82c33"}, "domainSeparator": "0x07c465e5105b7d761c8d320a45e7eae0776998c6f3f8c2558609ba08cef1bc9e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0a98031f532c268c6a4d231e015432084c4923c8": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x0a98031f532c268c6a4d231e015432084c4923c8"}, "domainSeparator": "0x50d1e132ca907e0c5ca4807dac93c46213a85f140ac9928b8682140f6d375162", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0617d5ffb29c03ac35f1863b8a50ce1b52d446f6": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x0617d5ffb29c03ac35f1863b8a50ce1b52d446f6"}, "domainSeparator": "0xeed361ff994b295351d15dd581b2445250ca9a2a38ba99f86947ad9ccf6342c9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0be902716176d66364f1c2ecf25829a6d95c5bee": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x0be902716176d66364f1c2ecf25829a6d95c5bee"}, "domainSeparator": "0xdd6a1cc58793a893275462014c5727c7f4c92e0020bc7f126b03d2f345e70cf8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1d4d105bc6db52d614644aeb849895dfa8313849": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x1d4d105bc6db52d614644aeb849895dfa8313849"}, "domainSeparator": "0xdb3375baeb6630c55091910bc406b4488b109a21f4c4dd4dd0c89c0337654e70", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x17ee04ec364577937855d2e9a7add8d2a957e4fa": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x17ee04ec364577937855d2e9a7add8d2a957e4fa"}, "domainSeparator": "0x840ebc80046fbef03487e91eb373828b0131bb72b5ce131a272455cc9279f73f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x32ce7e48debdccbfe0cd037cc89526e4382cb81b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x32ce7e48debdccbfe0cd037cc89526e4382cb81b"}, "domainSeparator": "0x211801e9bc11f181ecd400dc01b083ca0e9e6824ab1e5a217d97a6cc1a9d9da8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3a11629d7b26f5ef39bb25b5dd5dea5da8964b25": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x3a11629d7b26f5ef39bb25b5dd5dea5da8964b25"}, "domainSeparator": "0x95217353637f92e5e186d99b73974db56cf37e720585e0e730cd67edb31ced60", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2fdbadf3c4d5a8666bc06645b8358ab803996e28": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x2fdbadf3c4d5a8666bc06645b8358ab803996e28"}, "domainSeparator": "0x289e9e265b4cf4993acc3782b9d70b233d562238232e43f0e88770500c7a9713", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x39c0edef530d284b8f7820061114157c5bd78093": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x39c0edef530d284b8f7820061114157c5bd78093"}, "domainSeparator": "0x2cd4b30af7b0fcad6cda293616d1d7c55ea239adb3a595c48c33e5485c05c13d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x308e019143b560215775a0c6efbd2673413d76e6": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x308e019143b560215775a0c6efbd2673413d76e6"}, "domainSeparator": "0xfbe4ee0ac36230c4adf290b2311efbebbd62dff43ce4e721ed0b81a7dcbe6c0a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2d3b42a5fd084f1e75ac7e3fcd081386a431f17a": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x2d3b42a5fd084f1e75ac7e3fcd081386a431f17a"}, "domainSeparator": "0xaafbfa8d52a80ffc15d6847e2dafb1c41fa876119e4376ed0350da867b7687bc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x31db6e081241e7b9023c3b2deee25d37dcc0cf16": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x31db6e081241e7b9023c3b2deee25d37dcc0cf16"}, "domainSeparator": "0x1190681de98ded0c45707ae40792e215116f0b488e186a3e9e9a42846b1959bc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x283c7d44a93db3c211b61fdbe3c3bb2678f5d657": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x283c7d44a93db3c211b61fdbe3c3bb2678f5d657"}, "domainSeparator": "0x786ea3e5c861ea2db8a4e2baa37630c97a2cf8e579abbc092606dcfc1a6c70f5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x44ee37ba8c98493f2590811c197ddd474c911d46": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x44ee37ba8c98493f2590811c197ddd474c911d46"}, "domainSeparator": "0xa08c6253710c28cae678529f8323f9b807101c0c2cdab11c096066de6e9972d9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3da1313ae46132a397d90d95b1424a9a7e3e0fce": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x3da1313ae46132a397d90d95b1424a9a7e3e0fce"}, "domainSeparator": "0x584d77b20ac2a076751c7c2f631e2e15895bea36b8938cc26f9fc348527fb8bc", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3041cbd36888becc7bbcbc0045e3b1f144466f5f": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x3041cbd36888becc7bbcbc0045e3b1f144466f5f"}, "domainSeparator": "0x063b4c34c3db6feb751bdcd14b4e581ded5a0164e75642b527c9b4d4ca44a6c6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x35baf3cb842394c52accbef27ec83f43495aa333": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x35baf3cb842394c52accbef27ec83f43495aa333"}, "domainSeparator": "0x351de478d3c040934a994118b36fa2def120e809443b829d05594e25aefd443e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x318b64b3222c40496830644632f24015d4b2b095": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x318b64b3222c40496830644632f24015d4b2b095"}, "domainSeparator": "0xd1ee5dd2967faa314258c11d62577f85ff0868c48badd6aa2f6610af183fc0e2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3dd49f67e9d5bc4c5e6634b3f70bfd9dc1b6bd74": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x3dd49f67e9d5bc4c5e6634b3f70bfd9dc1b6bd74"}, "domainSeparator": "0xa6179e86963ac45d0dce4f9c811b9c108914a2277e033f601a2e2368f531d25b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x43ae24960e5534731fc831386c07755a2dc33d47": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x43ae24960e5534731fc831386c07755a2dc33d47"}, "domainSeparator": "0x89e3f21ff37fec83316cf6d7db49424f207eccf8063c5f7ec08b82bce5dc2f5f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4f9fcdae3950a033074b93e269b6c382109fae71": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x4f9fcdae3950a033074b93e269b6c382109fae71"}, "domainSeparator": "0xfb8a48c042f00fd3fc608529da825001f2695bc433d6ce8cdb254aa6c0ee0b72", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x626fdfd9ece333f2f2e59d57a2f23d4595b5c820": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x626fdfd9ece333f2f2e59d57a2f23d4595b5c820"}, "domainSeparator": "0x68941e6ca665b9413fcf8e885f437c58e687a99298a9993a4b8728753571af48", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6a3d23fa07c455f88d70c29d230467c407a3964b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x6a3d23fa07c455f88d70c29d230467c407a3964b"}, "domainSeparator": "0xcb0e93b460909a58fa1fe3be81a33483bbebc0acc8070d1aee645dd8d9042021", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5c47016e8a4a3c6a7c46a765f81dce205d00393e": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x5c47016e8a4a3c6a7c46a765f81dce205d00393e"}, "domainSeparator": "0x50ac12831399b34810b704891a37f0a73498d205b5037fe78c465c0e8dc233be", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4c8341379e95f70c08defb76c4f9c036525edc30": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x4c8341379e95f70c08defb76c4f9c036525edc30"}, "domainSeparator": "0x1063b2835a062b4101b6eb1d2370e4115beeb8d0082c49dad7b064089b4ff8bd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5ac13261c181a9c3938bfe1b649e65d10f98566b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x5ac13261c181a9c3938bfe1b649e65d10f98566b"}, "domainSeparator": "0x42f6d7e629bc436115da678a96a4bb79c893717343fdd96d1a3f3ac33c3c24ff", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4c508b9861116a15351ae0b67aae548fcc05f99e": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x4c508b9861116a15351ae0b67aae548fcc05f99e"}, "domainSeparator": "0x60735ede1d5923b44b888ea9e8c805b3b3cb661dd47a7c5e5d01ef8e5645bc33", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x54572fda1844ab7c674ba151c67e7d9881f2b276": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x54572fda1844ab7c674ba151c67e7d9881f2b276"}, "domainSeparator": "0xf6ca72dea48fc8794dfd88152d802bbec0d3db66574b75276ceb26ebc2a067bf", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x59f96b8571e3b11f859a09eaf5a790a138fc64d0": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x59f96b8571e3b11f859a09eaf5a790a138fc64d0"}, "domainSeparator": "0xd43d3aa16d34a5403161c3ad64cc1ed1fde8b2173ad2c96f682b6d14e22f32b7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x554fcab23c38795c9e32b73562eed9fa431d7a7b": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x554fcab23c38795c9e32b73562eed9fa431d7a7b"}, "domainSeparator": "0xee2cdb99dcb0dffa3c134fed3fb82a6d182b03bf4d346ba35da14ea55ce055c9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4b824d4765708c44475c781fa4594ae37d1f130f": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x4b824d4765708c44475c781fa4594ae37d1f130f"}, "domainSeparator": "0xae1889cf0d8859ce4e15c58703b02f6fd9e388bf77541d5e313312fd5a0a049f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4a93a3bb71966dffde24b979945871e28f82236e": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0x4a93a3bb71966dffde24b979945871e28f82236e"}, "domainSeparator": "0x97e34aa38ada309adbe57dd36d5e18e3bc052e1ec7cedf1c7aff4c7c61a94ee7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xffa98a091331df4600f87c9164cd27e8a5cd2405": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xffa98a091331df4600f87c9164cd27e8a5cd2405"}, "domainSeparator": "0x46e55a981fc287f58b5b2adbae722331a65e8c4378af9210231f8acc440133ca", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xff58711683be66dad6e0e20e0043af46fc7f5f49": {"kind": "permit", "domain": {"name": "Uniswap V2", "version": "1", "chainId": 1, "verifyingContract": "0xff58711683be66dad6e0e20e0043af46fc7f5f49"}, "domainSeparator": "0x34a387bbad4d71adf2e5d40398fae893aafb6f91b465f78d41d7b625533b0791", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2966ff4d75842278c798f0b6d362bda81a1012b1": {"kind": "permit", "domain": {"name": "Up Ai", "version": "1", "chainId": 1, "verifyingContract": "0x2966ff4d75842278c798f0b6d362bda81a1012b1"}, "domainSeparator": "0x2d9201e295b47e0319bf8bf37cd6462c4b4f404b0d3eb34d2e7c24959f418924", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2dcf322d66cb77a05f66fdff93e3678df7e44dea": {"kind": "permit", "domain": {"name": "USDCow", "version": "1", "chainId": 1, "verifyingContract": "0x2dcf322d66cb77a05f66fdff93e3678df7e44dea"}, "domainSeparator": "0xa88e5393134ca4eaeab31fb768738fb3062267b85a6be29fb586ee48ad06f1d5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x255e182b989edd63dd3d91f78968cda5d4718d2a": {"kind": "permit", "domain": {"name": "USDCow", "version": "1", "chainId": 1, "verifyingContract": "0x255e182b989edd63dd3d91f78968cda5d4718d2a"}, "domainSeparator": "0x1932399f30c2d3068a0ed69a2a56fadd80e9eb2a58808b7c176fdc2b6b5843b9", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2b4200a8d373d484993c37d63ee14aee0096cd12": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "USDFreeLiquidity", "version": "1", "chainId": 1, "verifyingContract": "0x2b4200a8d373d484993c37d63ee14aee0096cd12"}, "domainSeparator": "0xec552a58f0f608548a5124c9567f1dae8ec90b726974c9cb083dd4175e1f1e3b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x2a54ba2964c8cd459dc568853f79813a60761b58": {"kind": "permit", "domain": {"name": "USDI Token", "version": "1", "chainId": 1, "verifyingContract": "0x2a54ba2964c8cd459dc568853f79813a60761b58"}, "domainSeparator": "0xd8ff1ac54b7be32691d135093c39b171c602b4b0c2a7f3f50515c0edccea9664", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x03eb7ce2907e202bb70bae3d7b0c588573d3cecc": {"kind": "permit", "domain": {"name": "Minimal USD", "version": "1", "chainId": 1, "verifyingContract": "0x03eb7ce2907e202bb70bae3d7b0c588573d3cecc"}, "domainSeparator": "0x262df23ae4f55eabdf69a193d74d500359aa82bb242237b2c2c5759f968a2597", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x88536c9b2c4701b8db824e6a16829d5b5eb84440": {"kind": "permit", "domain": {"name": "Universal Store of Value", "version": "1", "chainId": 1, "verifyingContract": "0x88536c9b2c4701b8db824e6a16829d5b5eb84440"}, "domainSeparator": "0x73a257de29993e9d8262fe3699d4b655c8f1ae137671850cbf1d6a8096b59ed8", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0f9a4ea78ba7c8a2b6284c97ba7895c7d2d0726d": {"kind": "permit", "domain": {"name": "United", "version": "1", "chainId": 1, "verifyingContract": "0x0f9a4ea78ba7c8a2b6284c97ba7895c7d2d0726d"}, "domainSeparator": "0x690e4aa7f3ea101a1b0e82af301fd24bbed6736cdce93a85718290722c722619", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x61c067aca3facbfcec2b9ba619c0bbc88261b073a95ee061f0368f5814382aee", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Bitcoin Cash by <PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0b4ce03b6f3ea26017faa3a31b43d82ad606ede618226d810704f0429222559b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Value Bond", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x25c6ea7b9ab0e3c0a0a409add9d0dc3af134733c616f6032121a28e897f55894", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Strudel BTC", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xa1f59f62dadb019c627c2971bc4da1109963da2f17a117539c53213bf6ad6d52", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Verse", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1d2df9c8e3422bf9ca691e736d999c2cdd4f150dfaceea19721be0ddba100cb7", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x7044239856b88dec315b00eba040e2c4aabf8a69dd636bc8b54a43516d203eec", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6e1ab0d7f43e4877db93eca85f6ca5816d493751": {"kind": "permit", "domain": {"name": "Vitalik Protocol", "version": "1", "chainId": 1, "verifyingContract": "0x6e1ab0d7f43e4877db93eca85f6ca5816d493751"}, "domainSeparator": "0xa2b5ca38a7becf4e349898cba1871a8aa1ce0b94598129b4252e2969ce232e00", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4eac0c7f0fb8dbe30997b6ad2da0cab78aeff4ec": {"kind": "permit", "domain": {"name": "Value Network Token", "version": "1", "chainId": 1, "verifyingContract": "0x4eac0c7f0fb8dbe30997b6ad2da0cab78aeff4ec"}, "domainSeparator": "0xfba52d785f88fb0abf79f2dea5354e00d192e12780fb6f52f9999a556bb94782", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x6d57b2e05f26c26b549231c866bdd39779e4a488": {"kind": "permit", "domain": {"name": "VNX Gold", "version": "1", "chainId": 1, "verifyingContract": "0x6d57b2e05f26c26b549231c866bdd39779e4a488"}, "domainSeparator": "0x37c236e00a530a8c4c1d2a404e86a77909769e36276cf523f127b98c1e97cc69", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5166e09628b696285e3a151e84fb977736a83575": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x5166e09628b696285e3a151e84fb977736a83575"}, "domainSeparator": "0xd076739b82575861302c1b4c8ecce2dd1b900995fda293906ff148be48ab0e4a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x559ebc30b0e58a45cc9ff573f77ef1e5eb1b3e18": {"kind": "permit", "domain": {"name": "VOLT", "version": "1", "chainId": 1, "verifyingContract": "0x559ebc30b0e58a45cc9ff573f77ef1e5eb1b3e18"}, "domainSeparator": "0x3e53cfd249606045e3baeff5b4ed85af8d24287dff66605cf060531bdb449a43", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xba4cfe5741b357fa371b506e5db0774abfecf8fc": {"kind": "permit", "domain": {"name": "vVSP pool", "version": "1", "chainId": 1, "verifyingContract": "0xba4cfe5741b357fa371b506e5db0774abfecf8fc"}, "domainSeparator": "0xd4479e1d36a182a26303c0348c2d96f203bf58b7c171df457be79c4ae71e3b6b", "domainSeparatorFn": "domainSeparator()", "nonce": "nonces()"}, "0xf4617c7019434bb103c457286f0494bd9fb9a9af": {"kind": "permit", "domain": {"name": "WagmipetLoveMaker", "version": "1", "chainId": 1, "verifyingContract": "0xf4617c7019434bb103c457286f0494bd9fb9a9af"}, "domainSeparator": "0xcba937f750eb2771af6588c5da347beffb4f0a9704ed82b7e6dc81ba47b2b1c0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xedb171c18ce90b633db442f2a6f72874093b49ef": {"kind": "permit", "domain": {"name": "Wrapped Ampleforth", "version": "1", "chainId": 1, "verifyingContract": "0xedb171c18ce90b633db442f2a6f72874093b49ef"}, "domainSeparator": "0x6bdddf1d37d8e920803eca51274d456dd8d752bb58cc45c564a0f84c6039dcb3", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe20b9e246db5a0d21bf9209e4858bc9a3ff7a034": {"kind": "permit", "domain": {"name": "Wrapped <PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xe20b9e246db5a0d21bf9209e4858bc9a3ff7a034"}, "domainSeparator": "0xc4525062a9aa8945ce45aa5a0a0666260d9a01349d096a11b8d4743185939843", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xec17a4f75fea1d2ecd7c9810fc75b0a097b16c77": {"kind": "permit", "domain": {"name": "what", "version": "1", "chainId": 1, "verifyingContract": "0xec17a4f75fea1d2ecd7c9810fc75b0a097b16c77"}, "domainSeparator": "0x27cb0c5cc4f42ff730d8270896dc7083c2206e972c4119f125adaf3893f29235", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0c75dd36af9a59ba1d248a98fe91b2384cfea9be": {"kind": "permit", "domain": {"name": "OmniWhirl", "version": "1", "chainId": 1, "verifyingContract": "0x0c75dd36af9a59ba1d248a98fe91b2384cfea9be"}, "domainSeparator": "0xe52e0b9b4004f7fb87884cfccdf8768f662c4fb8b686490241c32903ca5e9805", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbea76c71929788ab20e17759eac115798f9aef27": {"kind": "permit", "domain": {"name": "Wrapped <PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0xbea76c71929788ab20e17759eac115798f9aef27"}, "domainSeparator": "0x35d7e1157d8b26280e2c49c15ab0a38e155156efcb3aafeec24890753de106bd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x175d9dfd6850aa96460e29bc0cead05756965e91": {"kind": "permit", "domain": {"name": "Starname", "version": "1", "chainId": 1, "verifyingContract": "0x175d9dfd6850aa96460e29bc0cead05756965e91"}, "domainSeparator": "0xe29d9107619b4451f8ea0c64e3964800f239ebdaf20734fc61840128762212b4", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3b79a28264fc52c7b4cea90558aa0b162f7faf57": {"kind": "permit", "domain": {"name": "Wrapped MEMO", "version": "1", "chainId": 1, "verifyingContract": "0x3b79a28264fc52c7b4cea90558aa0b162f7faf57"}, "domainSeparator": "0x6604159382950c884928ba97867b559ea08f256d2ee2446aaa360fe4f8d8fa83", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbd356a39bff2cada8e9248532dd879147221cf76": {"kind": "permit", "domain": {"name": "WOM Token", "version": "1", "chainId": 1, "verifyingContract": "0xbd356a39bff2cada8e9248532dd879147221cf76"}, "domainSeparator": "0xe20cc45f50d352319d757034680cf904bdc16d31ae12276507e721f199403e1a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3405a1bd46b85c5c029483fbecf2f3e611026e45": {"kind": "permit", "domain": {"name": "WOWswap", "version": "1", "chainId": 1, "verifyingContract": "0x3405a1bd46b85c5c029483fbecf2f3e611026e45"}, "domainSeparator": "0xd690e224bf5f380e1c5e10bc767de81ae2a1aad354fa0fde3fcf6a23904f1880", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xe4174143f0d6b1ebc0e25e1acd975c8f9fd06539": {"kind": "permit", "domain": {"name": "Wrapped aRIA Currency", "version": "1", "chainId": 1, "verifyingContract": "0xe4174143f0d6b1ebc0e25e1acd975c8f9fd06539"}, "domainSeparator": "0x87e9803ad5c101e23b2b09820b37bd4ff441aaf10358d07c2370cee1a1173dbd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x622236bb180256b6ae1a935dae08dc0356141632": {"kind": "permit", "domain": {"name": "Mirror Write Token", "version": "1", "chainId": 1, "verifyingContract": "0x622236bb180256b6ae1a935dae08dc0356141632"}, "domainSeparator": "0xbb3b02f70d0455a66f398878e70aa6ad732e2392117f0766ce164665eac3b02d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Wallstreetbets", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xffff3202493957784870afaffee8367e67cd4b3c22ecec30ae9cdbdb6743c29d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "WyreBitcoin", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x751e76ba5a76bbde80a023012608bfb96d69f47bc251c98c6c29ba80e5358ec5", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "XAI Stablecoin", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x1e418fc7be6491633ed16e0bd69add582f236053a72e79d7fa4fbba6061bc16e", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "Tether Gold", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0xf04743eb802cd8859d4af406f2b8e6b7db23ba7b311694e0fa920cc4f63f0192", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "xBTRFLY", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x0972b2451490cf1b96f341254934d78f8d652e3d181da7d7ddfe9db8ac98c077", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "******************************************": {"kind": "permit", "domain": {"name": "CoinMetro Token", "version": "1", "chainId": 1, "verifyingContract": "******************************************"}, "domainSeparator": "0x5db771cdff0f82d48e3d80f5088bbccc2ee5811cb4f8d3de595a0eeefb47b302", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x471ea49dd8e60e697f4cac262b5fafcc307506e4": {"kind": "permit", "domain": {"name": "xcRMRK", "version": "1", "chainId": 1, "verifyingContract": "0x471ea49dd8e60e697f4cac262b5fafcc307506e4"}, "domainSeparator": "0xeb8538736e8f8916f58db88d500555084555f370ae1ab2208a5b639c36a34983", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x71eeba415a523f5c952cc2f06361d5443545ad28": {"kind": "permit", "domain": {"name": "XDAO", "version": "1", "chainId": 1, "verifyingContract": "0x71eeba415a523f5c952cc2f06361d5443545ad28"}, "domainSeparator": "0x1713cf7dcc77942b6f9cd640c02f74558964926a21f4966118aa2e4ee32e4e47", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x72b886d09c117654ab7da13a14d603001de0b777": {"kind": "permit", "domain": {"chainId": 1, "verifyingContract": "0x72b886d09c117654ab7da13a14d603001de0b777"}, "domainSeparator": "0x9784ae39ad9a36fc49eb00e1aa5b882820d0c8e266de982aa5b963bc4e5ce266", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0aed8a59bbaf98460d3b35490f577d5fec083323": {"kind": "permit", "domain": {"name": "Xenoanthropology", "version": "1", "chainId": 1, "verifyingContract": "0x0aed8a59bbaf98460d3b35490f577d5fec083323"}, "domainSeparator": "0xbd81cf3df5dbfbfa564547a717b3793aa2a581badae2dc9097a13f6d4c1da0ec", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x20e62b11c6d855ad8025eb900ab532ec0ffeb643": {"kind": "permit", "domain": {"name": "MSHE Test ", "version": "1", "chainId": 1, "verifyingContract": "0x20e62b11c6d855ad8025eb900ab532ec0ffeb643"}, "domainSeparator": "0x08b87fe520a1be5f574862a0fb4ca1f2bf4325442772ae36688d904d05e321e0", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3e5d9d8a63cc8a88748f229999cf59487e90721e": {"kind": "permit", "domain": {"name": "MetalSwap", "version": "1", "chainId": 1, "verifyingContract": "0x3e5d9d8a63cc8a88748f229999cf59487e90721e"}, "domainSeparator": "0xddfabc6bdc01b7e198b4165bd23f1f7c33f393e61ac20070323e51f9c2e24a1a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x56123908aca89bf60ce9659ae9f4589e46c8d2cf": {"kind": "permit", "domain": {"name": "<PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x56123908aca89bf60ce9659ae9f4589e46c8d2cf"}, "domainSeparator": "0xb68a53e4cd19d290b9917238f40cb5f6ed5dc560577c2bb8a4b92fa31ed677a1", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x3ac31979fb379e753e13f6936befc85425ea6481": {"kind": "permit", "domain": {"name": "Fealty", "version": "1", "chainId": 1, "verifyingContract": "0x3ac31979fb379e753e13f6936befc85425ea6481"}, "domainSeparator": "0xd2560a0130e61e797774573e21f54151e8d8305ee543fac00ded2ce1a86adb75", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x665ff8faa06986bd6f1802fa6c1d2e7d780a7369": {"kind": "permit", "domain": {"name": "xVADER", "version": "1", "chainId": 1, "verifyingContract": "0x665ff8faa06986bd6f1802fa6c1d2e7d780a7369"}, "domainSeparator": "0x0641fdf24615105a627556476e707ee811798957745f2a597d7c8c48de290894", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5f2de1593e7e297c56dfb10d60e356a937dc4939": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfone.trade", "version": "1", "chainId": 1, "verifyingContract": "0x5f2de1593e7e297c56dfb10d60e356a937dc4939"}, "domainSeparator": "0x8a6331ba942740388e2d470544e407aece5ba92a60e37c9ff5964123bec9f19f", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xed324e10e9ca6797412a1b7d1666594fc73eea50": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yficoin.finance", "version": "1", "chainId": 1, "verifyingContract": "0xed324e10e9ca6797412a1b7d1666594fc73eea50"}, "domainSeparator": "0x51f8373e43566645d04bd7ff6a489771bfa33c45cf1b0ab43b35e4cd4d321820", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0eac9bb695be3e4b86d751cdc36716a75afdb7b2": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfgold.link", "version": "1", "chainId": 1, "verifyingContract": "0x0eac9bb695be3e4b86d751cdc36716a75afdb7b2"}, "domainSeparator": "0x78db2251d508f7ab8340031dee236b62ace3eba45bdee0cd8164d5ae7dee2113", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf4bcc9537e4a6ff9d13a92b6273cc2349b659242": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfstake.network", "version": "1", "chainId": 1, "verifyingContract": "0xf4bcc9537e4a6ff9d13a92b6273cc2349b659242"}, "domainSeparator": "0x7a83d355f52112de22c6a19ae6c6b02373e3969e6f62e6f9c55de99883e894ff", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb6c7a20044d38d8b8ac2791c30351579b65f0803": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfking.network", "version": "1", "chainId": 1, "verifyingContract": "0xb6c7a20044d38d8b8ac2791c30351579b65f0803"}, "domainSeparator": "0x40d6da60f188c6b0959d7f255fdc5f8879fa8a3024311038e2c81b4763019178", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x7ea33cadd5a0a7264b7727909ef49b2f90692a01": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfipork.finance", "version": "1", "chainId": 1, "verifyingContract": "0x7ea33cadd5a0a7264b7727909ef49b2f90692a01"}, "domainSeparator": "0x99a124a3ee4c263b297ab18450a6d9b3205c2e7cc81284c8687de86c0ba602a2", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x442dc4dbb08e8a2c4dc8c28cc2c98369aecccd26": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfipool.finance", "version": "1", "chainId": 1, "verifyingContract": "0x442dc4dbb08e8a2c4dc8c28cc2c98369aecccd26"}, "domainSeparator": "0x1c740c068befe7b661aefa9d303653af6007fb1a5671009293f90825c3ed4cfd", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xbe053abff9a8cad24296102ba4fe83c1ee709062": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfired.finance", "version": "1", "chainId": 1, "verifyingContract": "0xbe053abff9a8cad24296102ba4fe83c1ee709062"}, "domainSeparator": "0x040784f1bbad62d028f1bcd28deeb19df692b602023b3260c5e79bacc59a3d32", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xcf6085096a1ac5ccdbc4e53ea5b62d23cf29f962": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfistable.finance", "version": "1", "chainId": 1, "verifyingContract": "0xcf6085096a1ac5ccdbc4e53ea5b62d23cf29f962"}, "domainSeparator": "0x1f4371ea2f604300cef7e6b2a63db40c8a25ea6b9dded910831944ab8f1b4e74", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x0f6e71454c39126baa89ed8dc36e3d4bc5d06f3d": {"kind": "dai<PERSON><PERSON><PERSON>", "domain": {"name": "yfworld.network", "version": "1", "chainId": 1, "verifyingContract": "0x0f6e71454c39126baa89ed8dc36e3d4bc5d06f3d"}, "domainSeparator": "0x73b2909c2449beacd05c00dc46d55f330589ee4a3aad8771cb4d4173957daf31", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xf55a93b613d172b86c2ba3981a849dae2aecde2f": {"kind": "permit", "domain": {"name": "YFX", "version": "1", "chainId": 1, "verifyingContract": "0xf55a93b613d172b86c2ba3981a849dae2aecde2f"}, "domainSeparator": "0xed2d26fb54a6adf158eab6d6e05e7bdc659be7308f06042935f0b5cce5bbf378", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x30bb8d13047204ca2ab8f94d4c96e9dab85bac28": {"kind": "permit", "domain": {"name": "YUAN", "chainId": 1, "verifyingContract": "0x30bb8d13047204ca2ab8f94d4c96e9dab85bac28"}, "domainSeparator": "0xc7e086b8945b088296324a1c2736eb0f8ccf17318167a96e9dc7417ff1152aa6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x4a3e164684812dfb684ac36457e7fa805087c68e": {"kind": "permit", "domain": {"name": "YUAN", "chainId": 1, "verifyingContract": "0x4a3e164684812dfb684ac36457e7fa805087c68e"}, "domainSeparator": "0xc83bfe811fba10bba0a00033cd0e3783dccc92c71fd8058ac3558d6c30fa77f6", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x909e34d3f6124c324ac83dcca84b74398a6fa173": {"kind": "permit", "domain": {"name": "$ZKP Token", "version": "1", "chainId": 1, "verifyingContract": "0x909e34d3f6124c324ac83dcca84b74398a6fa173"}, "domainSeparator": "0xb48a1ca1f06010ede6b3e73f3379cfded146d01900221e4e88b4c25a4318f8ac", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0xb06110da6077a492641f0eb960e74b3d4e9a8428": {"kind": "permit", "domain": {"name": "Based Protocol", "version": "1", "chainId": 1, "verifyingContract": "0xb06110da6077a492641f0eb960e74b3d4e9a8428"}, "domainSeparator": "0x263905297bf6b6f5c0c9eec993bea7b1a631b460967ddeebec9b7d3e542c9362", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x5283d291dbcf85356a21ba090e6db59121208b44": {"kind": "permit", "domain": {"name": "Blur", "version": "1", "chainId": 1, "verifyingContract": "0x5283d291dbcf85356a21ba090e6db59121208b44"}, "domainSeparator": "0x7ab1c4b08cbfc7a1bec23e74548143217c862f40fbfb4f73b6d844e8a4dddb0a", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x1abaea1f7c830bd89acc67ec4af516284b1bc33c": {"kind": "permit", "domain": {"name": "Euro Coin", "version": "2", "chainId": 1, "verifyingContract": "0x1abaea1f7c830bd89acc67ec4af516284b1bc33c"}, "domainSeparator": "0x99f188f447f0c6eaf68589359cd2ead8c2faaaaee984ab926fdb734d0040073b", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}, "0x8457ca5040ad67fdebbcc8edce889a335bc0fbfb": {"kind": "permit", "domain": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1", "chainId": 1, "verifyingContract": "0x8457ca5040ad67fdebbcc8edce889a335bc0fbfb"}, "domainSeparator": "0xe14bb7bac71d1505cca6770614274ef3dac5acaeae4731265c769b64037ee82d", "domainSeparatorFn": "DOMAIN_SEPARATOR()", "nonce": "nonces()"}}
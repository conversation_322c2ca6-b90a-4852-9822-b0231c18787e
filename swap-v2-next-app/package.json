{"name": "0x-token-swap-dapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coinbase/wallet-sdk": "^4.1.0", "@rainbow-me/rainbowkit": "^2.1.7", "@tanstack/react-query": "^5.29.0", "ethers": "^6.11.1", "next": "14.1.4", "pino-pretty": "^11.0.0", "qs": "^6.12.0", "react": "^18", "react-dom": "^18", "viem": "^2.9.15", "wagmi": "^2.12.16"}, "devDependencies": {"@types/node": "^20", "@types/qs": "^6.9.14", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.1.4", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5"}}
import { type NextRequest } from "next/server";

// Mock quote data for testnet when API is unavailable
function getMockQuoteData(searchParams: URLSearchParams) {
  const sellAmount = searchParams.get('sellAmount') || '1000000000000000000';
  const sellToken = searchParams.get('sellToken') || '';
  const buyToken = searchParams.get('buyToken') || '';
  const taker = searchParams.get('taker') || '******************************************';

  // Base Sepolia addresses (normalized to lowercase for comparison)
  const WETH_BASE_SEPOLIA = '******************************************';
  const USDC_BASE_SEPOLIA = '******************************************';

  // Mock conversion rate: 1 WETH = ~3600 USDC
  const mockBuyAmount = sellToken.toLowerCase() === WETH_BASE_SEPOLIA.toLowerCase() &&
                       buyToken.toLowerCase() === USDC_BASE_SEPOLIA.toLowerCase()
    ? (BigInt(sellAmount) * BigInt(3600) / BigInt(1000000000000)).toString()
    : (BigInt(sellAmount) / BigInt(3600) * BigInt(1000000000000)).toString();

  return {
    sellToken: sellToken,
    buyToken: buyToken,
    sellAmount: sellAmount,
    buyAmount: mockBuyAmount,
    grossSellAmount: sellAmount,
    grossBuyAmount: mockBuyAmount,
    gasPrice: "1000000000",
    allowanceTarget: "******************************************",
    route: [],
    fees: {
      integratorFee: {
        amount: "10000",
        token: buyToken,
        type: "volume"
      },
      zeroExFee: {
        billingType: "on-chain",
        feeAmount: "1000000",
        feeToken: buyToken,
        feeType: "volume"
      },
      gasFee: null
    },
    auxiliaryChainData: {},
    to: taker,
    data: "0x", // Mock transaction data
    value: "0",
    gas: "150000",
    permit2: {
      type: "Permit2",
      hash: "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
      eip712: {
        types: {},
        domain: {},
        message: {},
        primaryType: "PermitTransferFrom"
      }
    },
    transaction: {
      data: "0x",
      gas: "150000",
      gasPrice: "1000000000",
      to: taker,
      value: "0"
    },
    tokenMetadata: {
      buyToken: { buyTaxBps: "0", sellTaxBps: "0" },
      sellToken: { buyTaxBps: "0", sellTaxBps: "0" }
    }
  };
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const chainId = searchParams.get('chainId');

  // For testnet chains, use mock data due to API limitations
  if (chainId === '84532' || chainId === '11155111') {
    console.log('Using mock quote data for testnet chain:', chainId);
    const mockData = getMockQuoteData(searchParams);
    return Response.json(mockData);
  }

  try {
    const res = await fetch(
      `https://api.0x.org/swap/permit2/quote?${searchParams}`,
      {
        headers: {
          "0x-api-key": process.env.NEXT_PUBLIC_ZEROEX_API_KEY as string,
          "0x-version": "v2",
        },
        signal: AbortSignal.timeout(5000),
      }
    );

    console.log(
      "quote api",
      `https://api.0x.org/swap/permit2/quote?${searchParams}`
    );

    // Check if the response is ok
    if (!res.ok) {
      console.error('0x API error:', res.status, res.statusText);
      const errorText = await res.text();
      console.error('Error response:', errorText);

      // Fallback to mock data on API error
      console.log('Falling back to mock quote data due to API error');
      const mockData = getMockQuoteData(searchParams);
      return Response.json(mockData);
    }

    const data = await res.json();
    console.log("quote data", data);

    return Response.json(data);
  } catch (error) {
    console.error('Quote API error:', error);

    // Fallback to mock data on any error
    console.log('Falling back to mock quote data due to fetch error');
    const mockData = getMockQuoteData(searchParams);
    return Response.json(mockData);
  }
}

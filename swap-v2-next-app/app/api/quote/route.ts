import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;

  try {
    const res = await fetch(
      `https://base.api.0x.org/swap/permit2/quote?${searchParams}`,
      {
        headers: {
          "0x-api-key": process.env.NEXT_PUBLIC_ZEROEX_API_KEY as string,
          "0x-version": "v2",
        },
      }
    );

    console.log(
      "quote api",
      `https://base.api.0x.org/swap/permit2/quote?${searchParams}`
    );

    // Check if the response is ok
    if (!res.ok) {
      console.error('0x API error:', res.status, res.statusText);
      const errorText = await res.text();
      console.error('Error response:', errorText);

      return Response.json(
        { error: `API Error: ${res.status} ${res.statusText}`, details: errorText },
        { status: res.status }
      );
    }

    const data = await res.json();
    console.log("quote data", data);

    return Response.json(data);
  } catch (error) {
    console.error('Quote API error:', error);
    return Response.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

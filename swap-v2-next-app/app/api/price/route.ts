import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;

  try {
    const res = await fetch(
      `https://base.api.0x.org/swap/permit2/price?${searchParams}`,
      {
        headers: {
          "0x-api-key": process.env.NEXT_PUBLIC_ZEROEX_API_KEY as string,
          "0x-version": "v2",
        },
      }
    );

    console.log(
      "price api",
      `https://base.api.0x.org/swap/permit2/price?${searchParams}`
    );

    // Check if the response is ok
    if (!res.ok) {
      console.error('0x API error:', res.status, res.statusText);
      const errorText = await res.text();
      console.error('Error response:', errorText);

      return Response.json(
        { error: `API Error: ${res.status} ${res.statusText}`, details: errorText },
        { status: res.status }
      );
    }

    const data = await res.json();
    console.log("price data", data);

    return Response.json(data);
  } catch (error) {
    console.error('Price API error:', error);
    return Response.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import { type NextRequest } from "next/server";

// Mock price data for testnet when API is unavailable
function getMockPriceData(searchParams: URLSearchParams) {
  const sellAmount = searchParams.get('sellAmount') || '1000000000000000000'; // 1 ETH
  const sellToken = searchParams.get('sellToken') || '';
  const buyToken = searchParams.get('buyToken') || '';

  // Base Sepolia addresses (normalized to lowercase for comparison)
  const WETH_BASE_SEPOLIA = '******************************************';
  const USDC_BASE_SEPOLIA = '******************************************';

  // Mock conversion rate: 1 WETH = ~3600 USDC (approximate)
  const mockBuyAmount = sellToken.toLowerCase() === WETH_BASE_SEPOLIA.toLowerCase() &&
                       buyToken.toLowerCase() === USDC_BASE_SEPOLIA.toLowerCase()
    ? (BigInt(sellAmount) * BigInt(3600) / BigInt(1000000000000)).toString() // WETH to USDC
    : (BigInt(sellAmount) / BigInt(3600) * BigInt(1000000000000)).toString(); // Reverse

  return {
    blockNumber: "12345678",
    buyAmount: mockBuyAmount,
    buyToken: buyToken,
    fees: {
      integratorFee: {
        amount: "10000",
        token: buyToken,
        type: "volume"
      },
      zeroExFee: {
        amount: "1000000",
        token: buyToken,
        type: "volume"
      },
      gasFee: null
    },
    gas: "150000",
    gasPrice: "1000000000",
    issues: {
      allowance: null,
      balance: null,
      simulationIncomplete: false,
      invalidSourcesPassed: []
    },
    liquidityAvailable: true,
    minBuyAmount: (BigInt(mockBuyAmount) * BigInt(99) / BigInt(100)).toString(),
    route: {
      fills: [{
        from: sellToken,
        to: buyToken,
        source: "Mock_DEX",
        proportionBps: "10000"
      }],
      tokens: [
        { address: sellToken, symbol: "WETH" },
        { address: buyToken, symbol: "USDC" }
      ]
    },
    sellAmount: sellAmount,
    sellToken: sellToken,
    tokenMetadata: {
      buyToken: { buyTaxBps: "0", sellTaxBps: "0" },
      sellToken: { buyTaxBps: "0", sellTaxBps: "0" }
    },
    totalNetworkFee: "150000000000000",
    zid: "mock_testnet_quote"
  };
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const chainId = searchParams.get('chainId');

  // For testnet chains, use mock data due to API limitations
  if (chainId === '84532' || chainId === '11155111') {
    console.log('Using mock data for testnet chain:', chainId);
    const mockData = getMockPriceData(searchParams);
    return Response.json(mockData);
  }

  try {
    const res = await fetch(
      `https://api.0x.org/swap/permit2/price?${searchParams}`,
      {
        headers: {
          "0x-api-key": process.env.NEXT_PUBLIC_ZEROEX_API_KEY as string,
          "0x-version": "v2",
        },
        signal: AbortSignal.timeout(5000), // 5 second timeout
      }
    );

    console.log(
      "price api",
      `https://api.0x.org/swap/permit2/price?${searchParams}`
    );

    // Check if the response is ok
    if (!res.ok) {
      console.error('0x API error:', res.status, res.statusText);
      const errorText = await res.text();
      console.error('Error response:', errorText);

      // Fallback to mock data on API error
      console.log('Falling back to mock data due to API error');
      const mockData = getMockPriceData(searchParams);
      return Response.json(mockData);
    }

    const data = await res.json();
    console.log("price data", data);

    return Response.json(data);
  } catch (error) {
    console.error('Price API error:', error);

    // Fallback to mock data on any error
    console.log('Falling back to mock data due to fetch error');
    const mockData = getMockPriceData(searchParams);
    return Response.json(mockData);
  }
}

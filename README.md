# 0x Examples

A collection of 0x API code examples

## v2 (Latest)

### Swap API

- [Swap API v2 Demo App (Permit2) using Next.js App Router](https://github.com/0xProject/0x-examples/tree/main/swap-v2-next-app)
- [Swap API v2 (Permit2) Headless Example](https://github.com/0xProject/0x-examples/tree/main/swap-v2-headless-example)
- [Swap API v2 (AllowanceHolder) Headless Example](https://github.com/0xProject/0x-examples/tree/main/swap-v2-allowance-holder-headless-example)
- [Use Swap API v2 in Your Smart Contract with Foundry](https://github.com/0xProject/0x-examples/tree/main/swap-v2-with-foundry)

### Gasless API

- [Gasless API v2 Headless Example](https://github.com/0xProject/0x-examples/blob/main/gasless-v2-headless-example/README.md)
- [Gasless API v2 Trading Bot](https://github.com/0xProject/0x-examples/tree/main/gasless-v2-trading-bot)


## v1 (Deprecated)

> [!WARNING]  
> 0x API v1 was sunset on April 11, 2025. Please migrate to v2. For details, see the [migration guide](https://0x.org/docs/upgrading).

### Swap API

- [Swap API v1 Demo App using Next.js App Router](https://github.com/0xProject/0x-examples/tree/main/swap-next-app)
- [Swap API v1 Demo App using Next.js Pages Router](https://github.com/0xProject/0x-nextjs-demo-app/tree/main)
- [Swap API v1 Demo App using HTML/CSS/JavaScript](https://github.com/0xProject/swap-demo-tutorial)
- [Swap API v1 Headless Example](https://github.com/0xProject/0x-examples/tree/main/swap-headless-example)

### Gasless API

- [Gasless API v1 Demo App using Next.js App Router](https://github.com/0xProject/0x-examples/tree/main/gasless-next-app)


## Contribution Guidelines

1. **Fork the Repository:** Start by forking the repository and creating a new branch for your contributions.

2. **Set Up Environment:** Follow the setup guide in the README to ensure your environment matches the development requirements.

3. **Code Standards:** Adhere to the ESLint rules provided in the project

4. **Documentation:** Include or update relevant documentation for new features or changes.

5. **Pull Request:**
- Provide a clear description of the changes and the issue(s) addressed
- Tag at least one maintainer for review
- Include screenshots or logs for UI changes or CLI commands

## Code of Conduct

1. **Be Respectful:** Treat others with respect and kindness in all interactions.

2. **Constructive Feedback:** Provide feedback that is thoughtful, helpful, and actionable.

3. **No Harassment:** Harassment, abusive language, or any form of discrimination will not be tolerated.

4. **Collaborative Environment:** Support an open and welcoming space for contributors from all backgrounds.

## Licenses

Copyright 2025 ZeroEx Labs

Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at [LICENSE](http://www.apache.org/licenses/LICENSE-2.0) for details.

Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.

## Support

### GitHub Issues
For bugs, feature requests, and other inquiries related to this example, please open an issue on the GitHub repository.

### Developer Support
The 0x developer support team is available to quickly answer your technical questions. Contact the [support team](https://0x.org/docs/introduction/community#contact-support) either through the "Intercom messenger" in the bottom right corner throughout the [0x.org](https://0x.org/).

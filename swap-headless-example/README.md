# [Deprecated] Swap v1 headless example (viem)

> [!WARNING]  
> 0x API v1 was sunset on April 11, 2025. Please migrate to v2. For details, see the [migration guide](https://0x.org/docs/upgrading).


A headless example of how to use 0x Swap [/price](https://0x.org/docs/0x-swap-api/api-references/get-swap-v1-price) and [/quote](https://0x.org/docs/0x-swap-api/api-references/get-swap-v1-quote) using [viem](https://viem.sh/)

Demonstrates the following on Base mainnet:

1. Build price params (sell 0.1 USDC → buy WETH) Fetch price.
2. Check token approval
3. Build quote params (sell 0.1 USDC → buy WETH). Fetch quote.
4. Send transaction.

> [!WARNING]  
> This is a demo, and is not ready for production use. The code has not been audited and does not account for all error handling. Use at your own risk.

#### Requirements

- Install [Bun](https://bun.sh/) (v1.1.0+)
- An Ethereum private key
- Setup a wallet with min 0.1 USDC and some ETH for gas

## Usage

1. Create an `.env` file and setup the required environment variables (your Ethereum private keys & 0x API key).

```sh
cp .env.example .env
```

2. Install dependencies

```sh
bun install
```

3. Run the script with either

```sh
# Run the script once
bun run index.ts
```

or

```sh
# Run the script in watch mode. Code automatically recompiles and re-executes upon changes.
bun --watch index.ts

```

4. Here is an example of the output. It fetches a `price` (USDC → WETH), approves a token allowance (if not already granted), fetches a `quote`, and submits the transaction, and shows the transaction hash:

```bash
 priceResponse:  {
  chainId: 8453,
  price: "0.*****************",
  grossPrice: "0.*****************",
  estimatedPriceImpact: "0",
  value: "0",
  gasPrice: "7920000",
  gas: "280000",
  estimatedGas: "280000",
  protocolFee: "0",
  minimumProtocolFee: "0",
  buyTokenAddress: "******************************************",
  buyAmount: "26574495246858",
  grossBuyAmount: "26614017062195",
  sellTokenAddress: "******************************************",
  sellAmount: "100000",
  grossSellAmount: "100000",
  sources: [
    {
      name: "Aerodrome",
      proportion: "0",
    }, {
      name: "Balancer_V2",
      proportion: "0",
    }, {
      name: "BaseSwap",
      proportion: "0",
    }, {
      name: "Maverick_V1",
      proportion: "0",
    }, {
      name: "SwapBased",
      proportion: "0",
    }, {
      name: "Uniswap_V3",
      proportion: "0",
    }, {
      name: "Alienbase",
      proportion: "1",
    }, {
      name: "SushiSwap",
      proportion: "0",
    }, {
      name: "Uniswap_V2",
      proportion: "0",
    }, {
      name: "Sushiswap_V3",
      proportion: "0",
    }
  ],
  allowanceTarget: "******************************************",
  sellTokenToEthRate: "3759.72157",
  buyTokenToEthRate: "1",
  fees: {
    zeroExFee: {
      feeType: "volume",
      feeToken: "******************************************",
      feeAmount: "39521815337",
      billingType: "on-chain",
    },
  },
  auxiliaryChainData: {
    l1GasEstimate: 167895581470,
  },
}
Approving 0x Exchange Proxy to spend USDC... {
  abi: [
    {
      type: "function",
      name: "approve",
      stateMutability: "nonpayable",
      inputs: [
        {
          name: "spender",
          type: "address",
        }, {
          name: "amount",
          type: "uint256",
        }
      ],
      outputs: [
        {
          type: "bool",
        }
      ],
    }
  ],
  address: "******************************************",
  args: [ "******************************************", 115792089237316195423570985008687907853269984665640564039457584007913129639935n
  ],
  dataSuffix: undefined,
  functionName: "approve",
  account: {
    address: "******************************************",
    experimental_signAuthMessage: [Function: experimental_signAuthMessage],
    signMessage: [Function: signMessage],
    signTransaction: [Function: signTransaction],
    signTypedData: [Function: signTypedData],
    source: "privateKey",
    type: "local",
    publicKey: "0x043f19b73e60ad76e64038a21eae3c5f2314af790f8a2daa30f33171824dd4d80eac8bd123a95143a88d5ab8c66d3fedc30239335a48be5f7f457ce179bd031906",
  },
}
Approved 0x EP to spend USDC. {
  blockHash: "0xbe3056b66f7366904c4104f8b736505ceef7b7fcdaa0b7ba6e45150c1242963e",
  blockNumber: 15108030n,
  contractAddress: null,
  cumulativeGasUsed: 7813938n,
  effectiveGasPrice: 7896407n,
  from: "******************************************",
  gasUsed: 38685n,
  l1Fee: 39491097102n,
  l1GasPrice: 13264936809n,
  l1GasUsed: 2704n,
  logs: [
    {
      address: "******************************************",
      topics: [ "0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925",
        "0x0000000000000000000000004d2a422db44144996e855ce15fb581a477dbb947", "0x000000000000000000000000def1c0ded9bec7f1a1670819833240f027b25eff"
      ],
      data: "0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
      blockNumber: 15108030n,
      transactionHash: "0x841d36f25652c9c72d92a75e42167bd9492b7036d7f15ca768de8bcff5d3b78d",
      transactionIndex: 48,
      blockHash: "0xbe3056b66f7366904c4104f8b736505ceef7b7fcdaa0b7ba6e45150c1242963e",
      logIndex: 165,
      removed: false,
    }
  ],
  logsBloom: "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000200000000000000000002000000000000000000000000020080000000000000000000000800000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000004000000000000000000000000000000000008000000008000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000",
  status: "success",
  to: "******************************************",
  transactionHash: "0x841d36f25652c9c72d92a75e42167bd9492b7036d7f15ca768de8bcff5d3b78d",
  transactionIndex: 48,
  type: "eip1559",
  l1FeeScalar: null,
}
quoteResponse:  {
  chainId: 8453,
  price: "0.*****************",
  grossPrice: "0.*****************",
  estimatedPriceImpact: "0",
  value: "0",
  gasPrice: "7920000",
  gas: "332064",
  estimatedGas: "332064",
  protocolFee: "0",
  minimumProtocolFee: "0",
  buyTokenAddress: "******************************************",
  buyAmount: "26574495246858",
  grossBuyAmount: "26614017062195",
  sellTokenAddress: "******************************************",
  sellAmount: "100000",
  grossSellAmount: "100000",
  sources: [
    {
      name: "Aerodrome",
      proportion: "0",
    }, {
      name: "Balancer_V2",
      proportion: "0",
    }, {
      name: "BaseSwap",
      proportion: "0",
    }, {
      name: "Maverick_V1",
      proportion: "0",
    }, {
      name: "SwapBased",
      proportion: "0",
    }, {
      name: "Uniswap_V3",
      proportion: "0",
    }, {
      name: "Alienbase",
      proportion: "1",
    }, {
      name: "SushiSwap",
      proportion: "0",
    }, {
      name: "Uniswap_V2",
      proportion: "0",
    }, {
      name: "Sushiswap_V3",
      proportion: "0",
    }
  ],
  allowanceTarget: "******************************************",
  sellTokenToEthRate: "3759.72157",
  buyTokenToEthRate: "1",
  to: "******************************************",
  from: "******************************************",
  data: "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",
  decodedUniqueId: "0x767ead68588a8cb33c60bbb31642c54c",
  guaranteedPrice: "0.00026308355076236",
  orders: [
    {
      type: 0,
      source: "Alienbase",
      makerToken: "******************************************",
      takerToken: "******************************************",
      makerAmount: "26614017062195",
      takerAmount: "100000",
      fillData: {
        tokenAddressPath: [ "******************************************", "******************************************"
        ],
        router: "0x8c1a3cf8f83074169fe5d7ad50b978e1cd6b37c7",
      },
      fill: {
        input: "100000",
        output: "26614017062195",
        adjustedOutput: "25703217062195",
        gas: 115000,
      },
    }
  ],
  fees: {
    zeroExFee: {
      feeType: "volume",
      feeToken: "******************************************",
      feeAmount: "39521815337",
      billingType: "on-chain",
    },
  },
  auxiliaryChainData: {
    l1GasEstimate: 167895581470,
  },
}
Tx hash:  0x7477e6d489cc73358a71a48d7397901ec3656b3d9338b5aba665f3e0ec5165a7
See tx details at https://basescan.org/tx/0x7477e6d489cc73358a71a48d7397901ec3656b3d9338b5aba665f3e0ec5165a7
```
